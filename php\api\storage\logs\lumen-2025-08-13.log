[2025-08-13 07:40:09] production.ERROR: Command "tinker" is not defined. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\CommandNotFoundException(code: 0): Command \"tinker\" is not defined. at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php:737)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(266): Symfony\\Component\\Console\\Application->find('tinker')
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Console\\Kernel.php(160): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\artisan(35): Laravel\\Lumen\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 {main}
"} 
[2025-08-13 11:43:16] production.ERROR: Command "tinker" is not defined. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\CommandNotFoundException(code: 0): Command \"tinker\" is not defined. at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php:737)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(266): Symfony\\Component\\Console\\Application->find('tinker')
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Console\\Kernel.php(160): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\artisan(35): Laravel\\Lumen\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 {main}
"} 
[2025-08-13 11:54:19] production.ERROR: The "--dry-run" option does not exist. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\RuntimeException(code: 0): The \"--dry-run\" option does not exist. at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Input\\ArgvInput.php:223)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Input\\ArgvInput.php(152): Symfony\\Component\\Console\\Input\\ArgvInput->addLongOption('dry-run', NULL)
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Input\\ArgvInput.php(85): Symfony\\Component\\Console\\Input\\ArgvInput->parseLongOption('--dry-run')
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Input\\ArgvInput.php(74): Symfony\\Component\\Console\\Input\\ArgvInput->parseToken('--dry-run', true)
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Input\\Input.php(56): Symfony\\Component\\Console\\Input\\ArgvInput->parse()
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Command\\Command.php(285): Symfony\\Component\\Console\\Input\\Input->bind(Object(Symfony\\Component\\Console\\Input\\InputDefinition))
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\console\\Command.php(181): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(1078): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Console\\Kernel.php(160): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\artisan(35): Laravel\\Lumen\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 {main}
"} 
[2025-08-13 11:54:33] production.ERROR: Command "tinker" is not defined. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\CommandNotFoundException(code: 0): Command \"tinker\" is not defined. at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php:737)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(266): Symfony\\Component\\Console\\Application->find('tinker')
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Console\\Kernel.php(160): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\artisan(35): Laravel\\Lumen\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 {main}
"} 
[2025-08-13 11:54:43] production.ERROR: Command "db:show" is not defined.

Did you mean one of these?
    db:seed
    db:wipe {"exception":"[object] (Symfony\\Component\\Console\\Exception\\CommandNotFoundException(code: 0): Command \"db:show\" is not defined.

Did you mean one of these?
    db:seed
    db:wipe at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php:737)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(266): Symfony\\Component\\Console\\Application->find('db:show')
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Console\\Kernel.php(160): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\artisan(35): Laravel\\Lumen\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 {main}
"} 
[2025-08-13 12:41:51] production.ERROR: Too few arguments to function App\Services\PyApi\AiGenerationService::__construct(), 0 passed in D:\longtool\phpStudy_64\WWW\tool_api\test_ai_generation_updates.php on line 20 and exactly 2 expected {"exception":"[object] (ArgumentCountError(code: 0): Too few arguments to function App\\Services\\PyApi\\AiGenerationService::__construct(), 0 passed in D:\\longtool\\phpStudy_64\\WWW\\tool_api\\test_ai_generation_updates.php on line 20 and exactly 2 expected at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\AiGenerationService.php:24)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\test_ai_generation_updates.php(20): App\\Services\\PyApi\\AiGenerationService->__construct()
#1 {main}
"} 
[2025-08-13 12:42:20] production.ERROR: Call to a member function connection() on null {"exception":"[object] (Error(code: 0): Call to a member function connection() on null at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Eloquent\\Model.php:1819)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Eloquent\\Model.php(1785): Illuminate\\Database\\Eloquent\\Model::resolveConnection(NULL)
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Eloquent\\Model.php(1576): Illuminate\\Database\\Eloquent\\Model->getConnection()
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Eloquent\\Model.php(1495): Illuminate\\Database\\Eloquent\\Model->newBaseQueryBuilder()
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Eloquent\\Model.php(1531): Illuminate\\Database\\Eloquent\\Model->newModelQuery()
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Eloquent\\Model.php(1484): Illuminate\\Database\\Eloquent\\Model->newQueryWithoutScopes()
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Eloquent\\Model.php(2334): Illuminate\\Database\\Eloquent\\Model->newQuery()
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Eloquent\\Model.php(2346): Illuminate\\Database\\Eloquent\\Model->__call('first', Array)
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\test_ai_generation_updates.php(32): Illuminate\\Database\\Eloquent\\Model::__callStatic('first', Array)
#8 {main}
"} 
[2025-08-13 15:32:49] production.INFO: WebSocket业务类型映射 {"frontend_business_type":"text","mapped_task_type":"text_generation","user_id":14,"session_id":"ws_TQgEN8vOZrAYodmikUHzI0VgdAEbhEn6"} 
[2025-08-13 15:32:49] production.INFO: WebSocket连接认证成功 {"session_id":"ws_TQgEN8vOZrAYodmikUHzI0VgdAEbhEn6","user_id":14,"client_type":"python_tool","connection_ip":"127.0.0.1"} 
[2025-08-13 15:45:51] production.INFO: 积分检查 {"user_id":14,"required_amount":0.5768,"current_balance":"94.40","sufficient":true,"business_type":"text_generation","business_id":null} 
[2025-08-13 15:45:51] production.INFO: 积分冻结成功 {"user_id":14,"amount":0.5768,"business_type":"text_generation","business_id":null,"transaction_id":29,"freeze_id":29} 
[2025-08-13 16:16:44] production.INFO: User offline {"user_id":14} 
[2025-08-13 16:17:27] production.ERROR: Redis通道订阅失败 {"error":"read error on connection to 127.0.0.1:6379"} 
[2025-08-13 16:19:10] production.INFO: WebSocket业务类型映射 {"frontend_business_type":"storyboard","mapped_task_type":"text_generation_storyboard","user_id":14,"session_id":"ws_VvDJKvhSYImYJvxWiJy79jLxqsCzPpDe"} 
[2025-08-13 16:19:11] production.INFO: WebSocket连接认证成功 {"session_id":"ws_VvDJKvhSYImYJvxWiJy79jLxqsCzPpDe","user_id":14,"client_type":"python_tool","connection_ip":"127.0.0.1"} 
[2025-08-13 16:19:13] production.INFO: WebSocket业务类型映射 {"frontend_business_type":"storyboard","mapped_task_type":"text_generation_storyboard","user_id":14,"session_id":"ws_XeLhDmQCfkQrn6vNY7DA91L0UF3erivH"} 
[2025-08-13 16:19:13] production.INFO: WebSocket连接认证成功 {"session_id":"ws_XeLhDmQCfkQrn6vNY7DA91L0UF3erivH","user_id":14,"client_type":"python_tool","connection_ip":"127.0.0.1"} 
[2025-08-13 16:21:32] production.INFO: 积分检查 {"user_id":14,"required_amount":0.5852,"current_balance":"93.82","sufficient":true,"business_type":"text_generation","business_id":null} 
[2025-08-13 16:21:32] production.INFO: 积分冻结成功 {"user_id":14,"amount":0.5852,"business_type":"text_generation","business_id":null,"transaction_id":30,"freeze_id":30} 
[2025-08-13 16:23:44] production.INFO: WebSocket推送进度 - 任务开始 {"task_id":"text_gen_1754973513_flvmG0eH","user_id":13,"progress":10,"message":"开始文本生成任务","push_type":"ai_generation_progress","redis_channel":"websocket:push:session_*","push_method":"Redis通道桥接","timestamp":1755073424,"job_class":"App\\Jobs\\ProcessTextGeneration","job_method":"handle"} 
[2025-08-13 16:23:44] production.WARNING: 未找到任务记录 {"task_id":"text_gen_1754973513_flvmG0eH","user_id":13} 
[2025-08-13 16:23:44] production.INFO: WebSocket推送结果 - 任务开始 {"task_id":"text_gen_1754973513_flvmG0eH","user_id":13,"push_success":"no","push_method":"Redis通道桥接"} 
[2025-08-13 16:23:44] production.INFO: WebSocket推送进度 - 连接AI平台 {"task_id":"text_gen_1754973513_flvmG0eH","user_id":13,"progress":30,"message":"连接AI平台","push_type":"ai_generation_progress","redis_channel":"websocket:push:session_*","push_method":"Redis通道桥接","timestamp":1755073424,"job_class":"App\\Jobs\\ProcessTextGeneration","job_method":"handle"} 
[2025-08-13 16:23:44] production.WARNING: 未找到任务记录 {"task_id":"text_gen_1754973513_flvmG0eH","user_id":13} 
[2025-08-13 16:23:44] production.INFO: WebSocket推送结果 - 连接AI平台 {"task_id":"text_gen_1754973513_flvmG0eH","user_id":13,"push_success":"no","push_method":"Redis通道桥接"} 
[2025-08-13 16:23:44] production.WARNING: 未找到活跃的WebSocket会话或business_type为空 {"task_id":"text_gen_1754973513_flvmG0eH","user_id":13} 
[2025-08-13 16:23:44] production.DEBUG: Lumen事务开始 {"context":"AiGenerationService.generateText","level":1,"action":"real_begin","framework":"Lumen 10"} 
[2025-08-13 16:23:44] production.WARNING: 事务回滚 {"context":"AiGenerationService.generateText","reason":"Undefined array key \"estimated_cost\"","original_level":1,"action":"real_rollback"} 
[2025-08-13 16:23:44] production.ERROR: 文本生成任务创建失败 {"method":"App\\Services\\PyApi\\AiGenerationService::generateText","error_context":{"user_id":13,"prompt":"","model_id":null,"project_id":null,"generation_params_count":3},"error":"Undefined array key \"estimated_cost\"","trace":"#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RegistersExceptionHandlers.php(47): Laravel\\Lumen\\Application->handleError(2, 'Undefined array...', 'D:\\\\longtool\\\\php...', 291)
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\AiGenerationService.php(291): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(2, 'Undefined array...', 'D:\\\\longtool\\\\php...', 291)
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Jobs\\ProcessTextGeneration.php(143): App\\Services\\PyApi\\AiGenerationService->generateText(13, '', NULL, NULL, NULL, Array, NULL, 'text_gen_175497...')
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(36): App\\Jobs\\ProcessTextGeneration->handle()
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Laravel\\Lumen\\Application), Array, Object(Closure))
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Laravel\\Lumen\\Application), Array, Array, NULL)
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\bus\\Dispatcher.php(128): Illuminate\\Container\\Container->call(Array)
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(144): Illuminate\\Bus\\Dispatcher->Illuminate\\Bus\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\bus\\Dispatcher.php(132): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\CallQueuedHandler.php(124): Illuminate\\Bus\\Dispatcher->dispatchNow(Object(App\\Jobs\\ProcessTextGeneration), false)
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(144): Illuminate\\Queue\\CallQueuedHandler->Illuminate\\Queue\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#15 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\CallQueuedHandler.php(126): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#16 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\CallQueuedHandler.php(70): Illuminate\\Queue\\CallQueuedHandler->dispatchThroughMiddleware(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(App\\Jobs\\ProcessTextGeneration))
#17 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Jobs\\Job.php(102): Illuminate\\Queue\\CallQueuedHandler->call(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Array)
#18 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(439): Illuminate\\Queue\\Jobs\\Job->fire()
#19 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(389): Illuminate\\Queue\\Worker->process('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Queue\\WorkerOptions))
#20 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(176): Illuminate\\Queue\\Worker->runJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 'database', Object(Illuminate\\Queue\\WorkerOptions))
#21 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(138): Illuminate\\Queue\\Worker->daemon('database', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#22 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(121): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'default')
#23 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#24 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#25 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#26 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Laravel\\Lumen\\Application), Array, Object(Closure))
#27 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Laravel\\Lumen\\Application), Array, Array, NULL)
#28 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#29 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#30 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\console\\Command.php(181): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#31 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(1078): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#34 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Console\\Kernel.php(160): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#35 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\artisan(35): Laravel\\Lumen\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#36 {main}"} 
[2025-08-13 16:23:44] production.INFO: WebSocket推送事件 - 任务失败 {"task_id":"text_gen_1754973513_flvmG0eH","user_id":13,"error_message":"文本生成任务创建失败","push_type":"ai_generation_failed","redis_channel":"websocket:push:session_*","push_method":"Redis通道桥接","timestamp":1755073424,"job_class":"App\\Jobs\\ProcessTextGeneration","job_method":"handle"} 
[2025-08-13 16:23:44] production.WARNING: 未找到任务记录 {"task_id":"text_gen_1754973513_flvmG0eH","user_id":13} 
[2025-08-13 16:23:44] production.INFO: WebSocket推送结果 - 任务失败 {"task_id":"text_gen_1754973513_flvmG0eH","user_id":13,"push_success":"no","push_method":"Redis通道桥接","error_message":"文本生成任务创建失败"} 
[2025-08-13 16:23:44] production.ERROR: 发布文本生成失败事件异常 {"task_id":"text_gen_1754973513_flvmG0eH","error":"cURL error 60: SSL certificate problem: unable to get local issuer certificate (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.tiptop.cn/py-api/events/publish"} 
[2025-08-13 16:23:44] production.ERROR: 文本生成任务失败 {"task_id":"text_gen_1754973513_flvmG0eH","user_id":13,"context":"prompt_edit","prompt":"写一首关于春天的诗","error":"文本生成任务创建失败","trace":"#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(36): App\\Jobs\\ProcessTextGeneration->handle()
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Laravel\\Lumen\\Application), Array, Object(Closure))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Laravel\\Lumen\\Application), Array, Array, NULL)
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\bus\\Dispatcher.php(128): Illuminate\\Container\\Container->call(Array)
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(144): Illuminate\\Bus\\Dispatcher->Illuminate\\Bus\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\bus\\Dispatcher.php(132): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\CallQueuedHandler.php(124): Illuminate\\Bus\\Dispatcher->dispatchNow(Object(App\\Jobs\\ProcessTextGeneration), false)
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(144): Illuminate\\Queue\\CallQueuedHandler->Illuminate\\Queue\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\CallQueuedHandler.php(126): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\CallQueuedHandler.php(70): Illuminate\\Queue\\CallQueuedHandler->dispatchThroughMiddleware(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(App\\Jobs\\ProcessTextGeneration))
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Jobs\\Job.php(102): Illuminate\\Queue\\CallQueuedHandler->call(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Array)
#15 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(439): Illuminate\\Queue\\Jobs\\Job->fire()
#16 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(389): Illuminate\\Queue\\Worker->process('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Queue\\WorkerOptions))
#17 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(176): Illuminate\\Queue\\Worker->runJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 'database', Object(Illuminate\\Queue\\WorkerOptions))
#18 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(138): Illuminate\\Queue\\Worker->daemon('database', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#19 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(121): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'default')
#20 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#21 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#23 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Laravel\\Lumen\\Application), Array, Object(Closure))
#24 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Laravel\\Lumen\\Application), Array, Array, NULL)
#25 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#26 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\console\\Command.php(181): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#28 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(1078): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Console\\Kernel.php(160): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\artisan(35): Laravel\\Lumen\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 {main}"} 
[2025-08-13 16:23:44] production.ERROR: 文本生成任务创建失败 {"exception":"[object] (Exception(code: 0): 文本生成任务创建失败 at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Jobs\\ProcessTextGeneration.php:226)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(36): App\\Jobs\\ProcessTextGeneration->handle()
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Laravel\\Lumen\\Application), Array, Object(Closure))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Laravel\\Lumen\\Application), Array, Array, NULL)
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\bus\\Dispatcher.php(128): Illuminate\\Container\\Container->call(Array)
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(144): Illuminate\\Bus\\Dispatcher->Illuminate\\Bus\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\bus\\Dispatcher.php(132): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\CallQueuedHandler.php(124): Illuminate\\Bus\\Dispatcher->dispatchNow(Object(App\\Jobs\\ProcessTextGeneration), false)
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(144): Illuminate\\Queue\\CallQueuedHandler->Illuminate\\Queue\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\CallQueuedHandler.php(126): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\CallQueuedHandler.php(70): Illuminate\\Queue\\CallQueuedHandler->dispatchThroughMiddleware(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(App\\Jobs\\ProcessTextGeneration))
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Jobs\\Job.php(102): Illuminate\\Queue\\CallQueuedHandler->call(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Array)
#15 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(439): Illuminate\\Queue\\Jobs\\Job->fire()
#16 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(389): Illuminate\\Queue\\Worker->process('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Queue\\WorkerOptions))
#17 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(176): Illuminate\\Queue\\Worker->runJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 'database', Object(Illuminate\\Queue\\WorkerOptions))
#18 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(138): Illuminate\\Queue\\Worker->daemon('database', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#19 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(121): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'default')
#20 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#21 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#23 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Laravel\\Lumen\\Application), Array, Object(Closure))
#24 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Laravel\\Lumen\\Application), Array, Array, NULL)
#25 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#26 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\console\\Command.php(181): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#28 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(1078): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Console\\Kernel.php(160): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\artisan(35): Laravel\\Lumen\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 {main}
"} 
[2025-08-13 16:23:44] production.INFO: WebSocket推送进度 - 任务开始 {"task_id":"text_gen_1754974116_UKtS5jOP","user_id":13,"progress":10,"message":"开始文本生成任务","push_type":"ai_generation_progress","redis_channel":"websocket:push:session_*","push_method":"Redis通道桥接","timestamp":1755073424,"job_class":"App\\Jobs\\ProcessTextGeneration","job_method":"handle"} 
[2025-08-13 16:23:44] production.WARNING: 未找到任务记录 {"task_id":"text_gen_1754974116_UKtS5jOP","user_id":13} 
[2025-08-13 16:23:44] production.INFO: WebSocket推送结果 - 任务开始 {"task_id":"text_gen_1754974116_UKtS5jOP","user_id":13,"push_success":"no","push_method":"Redis通道桥接"} 
[2025-08-13 16:23:44] production.INFO: WebSocket推送进度 - 连接AI平台 {"task_id":"text_gen_1754974116_UKtS5jOP","user_id":13,"progress":30,"message":"连接AI平台","push_type":"ai_generation_progress","redis_channel":"websocket:push:session_*","push_method":"Redis通道桥接","timestamp":1755073424,"job_class":"App\\Jobs\\ProcessTextGeneration","job_method":"handle"} 
[2025-08-13 16:23:44] production.WARNING: 未找到任务记录 {"task_id":"text_gen_1754974116_UKtS5jOP","user_id":13} 
[2025-08-13 16:23:44] production.INFO: WebSocket推送结果 - 连接AI平台 {"task_id":"text_gen_1754974116_UKtS5jOP","user_id":13,"push_success":"no","push_method":"Redis通道桥接"} 
[2025-08-13 16:23:44] production.WARNING: 未找到活跃的WebSocket会话或business_type为空 {"task_id":"text_gen_1754974116_UKtS5jOP","user_id":13} 
[2025-08-13 16:23:44] production.DEBUG: Lumen事务开始 {"context":"AiGenerationService.generateText","level":1,"action":"real_begin","framework":"Lumen 10"} 
[2025-08-13 16:23:44] production.WARNING: 事务回滚 {"context":"AiGenerationService.generateText","reason":"Undefined array key \"estimated_cost\"","original_level":1,"action":"real_rollback"} 
[2025-08-13 16:23:44] production.ERROR: 文本生成任务创建失败 {"method":"App\\Services\\PyApi\\AiGenerationService::generateText","error_context":{"user_id":13,"prompt":"","model_id":null,"project_id":null,"generation_params_count":3},"error":"Undefined array key \"estimated_cost\"","trace":"#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RegistersExceptionHandlers.php(47): Laravel\\Lumen\\Application->handleError(2, 'Undefined array...', 'D:\\\\longtool\\\\php...', 291)
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\AiGenerationService.php(291): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(2, 'Undefined array...', 'D:\\\\longtool\\\\php...', 291)
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Jobs\\ProcessTextGeneration.php(143): App\\Services\\PyApi\\AiGenerationService->generateText(13, '', NULL, NULL, NULL, Array, NULL, 'text_gen_175497...')
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(36): App\\Jobs\\ProcessTextGeneration->handle()
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Laravel\\Lumen\\Application), Array, Object(Closure))
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Laravel\\Lumen\\Application), Array, Array, NULL)
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\bus\\Dispatcher.php(128): Illuminate\\Container\\Container->call(Array)
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(144): Illuminate\\Bus\\Dispatcher->Illuminate\\Bus\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\bus\\Dispatcher.php(132): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\CallQueuedHandler.php(124): Illuminate\\Bus\\Dispatcher->dispatchNow(Object(App\\Jobs\\ProcessTextGeneration), false)
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(144): Illuminate\\Queue\\CallQueuedHandler->Illuminate\\Queue\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#15 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\CallQueuedHandler.php(126): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#16 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\CallQueuedHandler.php(70): Illuminate\\Queue\\CallQueuedHandler->dispatchThroughMiddleware(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(App\\Jobs\\ProcessTextGeneration))
#17 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Jobs\\Job.php(102): Illuminate\\Queue\\CallQueuedHandler->call(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Array)
#18 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(439): Illuminate\\Queue\\Jobs\\Job->fire()
#19 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(389): Illuminate\\Queue\\Worker->process('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Queue\\WorkerOptions))
#20 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(176): Illuminate\\Queue\\Worker->runJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 'database', Object(Illuminate\\Queue\\WorkerOptions))
#21 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(138): Illuminate\\Queue\\Worker->daemon('database', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#22 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(121): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'default')
#23 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#24 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#25 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#26 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Laravel\\Lumen\\Application), Array, Object(Closure))
#27 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Laravel\\Lumen\\Application), Array, Array, NULL)
#28 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#29 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#30 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\console\\Command.php(181): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#31 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(1078): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#34 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Console\\Kernel.php(160): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#35 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\artisan(35): Laravel\\Lumen\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#36 {main}"} 
[2025-08-13 16:23:44] production.INFO: WebSocket推送事件 - 任务失败 {"task_id":"text_gen_1754974116_UKtS5jOP","user_id":13,"error_message":"文本生成任务创建失败","push_type":"ai_generation_failed","redis_channel":"websocket:push:session_*","push_method":"Redis通道桥接","timestamp":1755073424,"job_class":"App\\Jobs\\ProcessTextGeneration","job_method":"handle"} 
[2025-08-13 16:23:44] production.WARNING: 未找到任务记录 {"task_id":"text_gen_1754974116_UKtS5jOP","user_id":13} 
[2025-08-13 16:23:44] production.INFO: WebSocket推送结果 - 任务失败 {"task_id":"text_gen_1754974116_UKtS5jOP","user_id":13,"push_success":"no","push_method":"Redis通道桥接","error_message":"文本生成任务创建失败"} 
[2025-08-13 16:23:44] production.ERROR: 发布文本生成失败事件异常 {"task_id":"text_gen_1754974116_UKtS5jOP","error":"cURL error 60: SSL certificate problem: unable to get local issuer certificate (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.tiptop.cn/py-api/events/publish"} 
[2025-08-13 16:23:44] production.ERROR: 文本生成任务失败 {"task_id":"text_gen_1754974116_UKtS5jOP","user_id":13,"context":"prompt_edit","prompt":"请写一首关于春天的诗","error":"文本生成任务创建失败","trace":"#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(36): App\\Jobs\\ProcessTextGeneration->handle()
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Laravel\\Lumen\\Application), Array, Object(Closure))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Laravel\\Lumen\\Application), Array, Array, NULL)
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\bus\\Dispatcher.php(128): Illuminate\\Container\\Container->call(Array)
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(144): Illuminate\\Bus\\Dispatcher->Illuminate\\Bus\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\bus\\Dispatcher.php(132): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\CallQueuedHandler.php(124): Illuminate\\Bus\\Dispatcher->dispatchNow(Object(App\\Jobs\\ProcessTextGeneration), false)
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(144): Illuminate\\Queue\\CallQueuedHandler->Illuminate\\Queue\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\CallQueuedHandler.php(126): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\CallQueuedHandler.php(70): Illuminate\\Queue\\CallQueuedHandler->dispatchThroughMiddleware(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(App\\Jobs\\ProcessTextGeneration))
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Jobs\\Job.php(102): Illuminate\\Queue\\CallQueuedHandler->call(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Array)
#15 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(439): Illuminate\\Queue\\Jobs\\Job->fire()
#16 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(389): Illuminate\\Queue\\Worker->process('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Queue\\WorkerOptions))
#17 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(176): Illuminate\\Queue\\Worker->runJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 'database', Object(Illuminate\\Queue\\WorkerOptions))
#18 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(138): Illuminate\\Queue\\Worker->daemon('database', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#19 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(121): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'default')
#20 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#21 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#23 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Laravel\\Lumen\\Application), Array, Object(Closure))
#24 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Laravel\\Lumen\\Application), Array, Array, NULL)
#25 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#26 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\console\\Command.php(181): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#28 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(1078): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Console\\Kernel.php(160): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\artisan(35): Laravel\\Lumen\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 {main}"} 
[2025-08-13 16:23:44] production.ERROR: 文本生成任务创建失败 {"exception":"[object] (Exception(code: 0): 文本生成任务创建失败 at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Jobs\\ProcessTextGeneration.php:226)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(36): App\\Jobs\\ProcessTextGeneration->handle()
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Laravel\\Lumen\\Application), Array, Object(Closure))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Laravel\\Lumen\\Application), Array, Array, NULL)
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\bus\\Dispatcher.php(128): Illuminate\\Container\\Container->call(Array)
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(144): Illuminate\\Bus\\Dispatcher->Illuminate\\Bus\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\bus\\Dispatcher.php(132): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\CallQueuedHandler.php(124): Illuminate\\Bus\\Dispatcher->dispatchNow(Object(App\\Jobs\\ProcessTextGeneration), false)
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(144): Illuminate\\Queue\\CallQueuedHandler->Illuminate\\Queue\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\CallQueuedHandler.php(126): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\CallQueuedHandler.php(70): Illuminate\\Queue\\CallQueuedHandler->dispatchThroughMiddleware(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(App\\Jobs\\ProcessTextGeneration))
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Jobs\\Job.php(102): Illuminate\\Queue\\CallQueuedHandler->call(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Array)
#15 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(439): Illuminate\\Queue\\Jobs\\Job->fire()
#16 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(389): Illuminate\\Queue\\Worker->process('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Queue\\WorkerOptions))
#17 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(176): Illuminate\\Queue\\Worker->runJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 'database', Object(Illuminate\\Queue\\WorkerOptions))
#18 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(138): Illuminate\\Queue\\Worker->daemon('database', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#19 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(121): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'default')
#20 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#21 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#23 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Laravel\\Lumen\\Application), Array, Object(Closure))
#24 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Laravel\\Lumen\\Application), Array, Array, NULL)
#25 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#26 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\console\\Command.php(181): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#28 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(1078): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Console\\Kernel.php(160): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\artisan(35): Laravel\\Lumen\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 {main}
"} 
[2025-08-13 16:23:44] production.INFO: WebSocket推送进度 - 任务开始 {"task_id":"text_gen_1754988026_B98EDyFQ","user_id":13,"progress":10,"message":"开始文本生成任务","push_type":"ai_generation_progress","redis_channel":"websocket:push:session_*","push_method":"Redis通道桥接","timestamp":1755073424,"job_class":"App\\Jobs\\ProcessTextGeneration","job_method":"handle"} 
[2025-08-13 16:23:44] production.WARNING: 未找到任务记录 {"task_id":"text_gen_1754988026_B98EDyFQ","user_id":13} 
[2025-08-13 16:23:44] production.INFO: WebSocket推送结果 - 任务开始 {"task_id":"text_gen_1754988026_B98EDyFQ","user_id":13,"push_success":"no","push_method":"Redis通道桥接"} 
[2025-08-13 16:23:44] production.INFO: WebSocket推送进度 - 连接AI平台 {"task_id":"text_gen_1754988026_B98EDyFQ","user_id":13,"progress":30,"message":"连接AI平台","push_type":"ai_generation_progress","redis_channel":"websocket:push:session_*","push_method":"Redis通道桥接","timestamp":1755073424,"job_class":"App\\Jobs\\ProcessTextGeneration","job_method":"handle"} 
[2025-08-13 16:23:44] production.WARNING: 未找到任务记录 {"task_id":"text_gen_1754988026_B98EDyFQ","user_id":13} 
[2025-08-13 16:23:44] production.INFO: WebSocket推送结果 - 连接AI平台 {"task_id":"text_gen_1754988026_B98EDyFQ","user_id":13,"push_success":"no","push_method":"Redis通道桥接"} 
[2025-08-13 16:23:44] production.WARNING: 未找到活跃的WebSocket会话或business_type为空 {"task_id":"text_gen_1754988026_B98EDyFQ","user_id":13} 
[2025-08-13 16:23:44] production.DEBUG: Lumen事务开始 {"context":"AiGenerationService.generateText","level":1,"action":"real_begin","framework":"Lumen 10"} 
[2025-08-13 16:23:44] production.WARNING: 事务回滚 {"context":"AiGenerationService.generateText","reason":"Undefined array key \"estimated_cost\"","original_level":1,"action":"real_rollback"} 
[2025-08-13 16:23:44] production.ERROR: 文本生成任务创建失败 {"method":"App\\Services\\PyApi\\AiGenerationService::generateText","error_context":{"user_id":13,"prompt":"","model_id":null,"project_id":null,"generation_params_count":3},"error":"Undefined array key \"estimated_cost\"","trace":"#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RegistersExceptionHandlers.php(47): Laravel\\Lumen\\Application->handleError(2, 'Undefined array...', 'D:\\\\longtool\\\\php...', 291)
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\AiGenerationService.php(291): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(2, 'Undefined array...', 'D:\\\\longtool\\\\php...', 291)
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Jobs\\ProcessTextGeneration.php(143): App\\Services\\PyApi\\AiGenerationService->generateText(13, '', NULL, NULL, NULL, Array, NULL, 'text_gen_175498...')
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(36): App\\Jobs\\ProcessTextGeneration->handle()
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Laravel\\Lumen\\Application), Array, Object(Closure))
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Laravel\\Lumen\\Application), Array, Array, NULL)
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\bus\\Dispatcher.php(128): Illuminate\\Container\\Container->call(Array)
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(144): Illuminate\\Bus\\Dispatcher->Illuminate\\Bus\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\bus\\Dispatcher.php(132): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\CallQueuedHandler.php(124): Illuminate\\Bus\\Dispatcher->dispatchNow(Object(App\\Jobs\\ProcessTextGeneration), false)
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(144): Illuminate\\Queue\\CallQueuedHandler->Illuminate\\Queue\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#15 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\CallQueuedHandler.php(126): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#16 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\CallQueuedHandler.php(70): Illuminate\\Queue\\CallQueuedHandler->dispatchThroughMiddleware(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(App\\Jobs\\ProcessTextGeneration))
#17 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Jobs\\Job.php(102): Illuminate\\Queue\\CallQueuedHandler->call(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Array)
#18 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(439): Illuminate\\Queue\\Jobs\\Job->fire()
#19 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(389): Illuminate\\Queue\\Worker->process('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Queue\\WorkerOptions))
#20 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(176): Illuminate\\Queue\\Worker->runJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 'database', Object(Illuminate\\Queue\\WorkerOptions))
#21 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(138): Illuminate\\Queue\\Worker->daemon('database', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#22 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(121): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'default')
#23 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#24 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#25 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#26 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Laravel\\Lumen\\Application), Array, Object(Closure))
#27 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Laravel\\Lumen\\Application), Array, Array, NULL)
#28 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#29 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#30 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\console\\Command.php(181): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#31 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(1078): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#34 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Console\\Kernel.php(160): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#35 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\artisan(35): Laravel\\Lumen\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#36 {main}"} 
[2025-08-13 16:23:44] production.INFO: WebSocket推送事件 - 任务失败 {"task_id":"text_gen_1754988026_B98EDyFQ","user_id":13,"error_message":"文本生成任务创建失败","push_type":"ai_generation_failed","redis_channel":"websocket:push:session_*","push_method":"Redis通道桥接","timestamp":1755073424,"job_class":"App\\Jobs\\ProcessTextGeneration","job_method":"handle"} 
[2025-08-13 16:23:44] production.WARNING: 未找到任务记录 {"task_id":"text_gen_1754988026_B98EDyFQ","user_id":13} 
[2025-08-13 16:23:44] production.INFO: WebSocket推送结果 - 任务失败 {"task_id":"text_gen_1754988026_B98EDyFQ","user_id":13,"push_success":"no","push_method":"Redis通道桥接","error_message":"文本生成任务创建失败"} 
[2025-08-13 16:23:44] production.ERROR: 发布文本生成失败事件异常 {"task_id":"text_gen_1754988026_B98EDyFQ","error":"cURL error 60: SSL certificate problem: unable to get local issuer certificate (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.tiptop.cn/py-api/events/publish"} 
[2025-08-13 16:23:44] production.ERROR: 文本生成任务失败 {"task_id":"text_gen_1754988026_B98EDyFQ","user_id":13,"context":"prompt_edit","prompt":"请写一首关于春天的诗","error":"文本生成任务创建失败","trace":"#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(36): App\\Jobs\\ProcessTextGeneration->handle()
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Laravel\\Lumen\\Application), Array, Object(Closure))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Laravel\\Lumen\\Application), Array, Array, NULL)
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\bus\\Dispatcher.php(128): Illuminate\\Container\\Container->call(Array)
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(144): Illuminate\\Bus\\Dispatcher->Illuminate\\Bus\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\bus\\Dispatcher.php(132): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\CallQueuedHandler.php(124): Illuminate\\Bus\\Dispatcher->dispatchNow(Object(App\\Jobs\\ProcessTextGeneration), false)
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(144): Illuminate\\Queue\\CallQueuedHandler->Illuminate\\Queue\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\CallQueuedHandler.php(126): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\CallQueuedHandler.php(70): Illuminate\\Queue\\CallQueuedHandler->dispatchThroughMiddleware(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(App\\Jobs\\ProcessTextGeneration))
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Jobs\\Job.php(102): Illuminate\\Queue\\CallQueuedHandler->call(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Array)
#15 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(439): Illuminate\\Queue\\Jobs\\Job->fire()
#16 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(389): Illuminate\\Queue\\Worker->process('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Queue\\WorkerOptions))
#17 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(176): Illuminate\\Queue\\Worker->runJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 'database', Object(Illuminate\\Queue\\WorkerOptions))
#18 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(138): Illuminate\\Queue\\Worker->daemon('database', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#19 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(121): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'default')
#20 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#21 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#23 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Laravel\\Lumen\\Application), Array, Object(Closure))
#24 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Laravel\\Lumen\\Application), Array, Array, NULL)
#25 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#26 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\console\\Command.php(181): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#28 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(1078): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Console\\Kernel.php(160): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\artisan(35): Laravel\\Lumen\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 {main}"} 
[2025-08-13 16:23:44] production.ERROR: 文本生成任务创建失败 {"exception":"[object] (Exception(code: 0): 文本生成任务创建失败 at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Jobs\\ProcessTextGeneration.php:226)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(36): App\\Jobs\\ProcessTextGeneration->handle()
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Laravel\\Lumen\\Application), Array, Object(Closure))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Laravel\\Lumen\\Application), Array, Array, NULL)
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\bus\\Dispatcher.php(128): Illuminate\\Container\\Container->call(Array)
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(144): Illuminate\\Bus\\Dispatcher->Illuminate\\Bus\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\bus\\Dispatcher.php(132): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\CallQueuedHandler.php(124): Illuminate\\Bus\\Dispatcher->dispatchNow(Object(App\\Jobs\\ProcessTextGeneration), false)
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(144): Illuminate\\Queue\\CallQueuedHandler->Illuminate\\Queue\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\CallQueuedHandler.php(126): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\CallQueuedHandler.php(70): Illuminate\\Queue\\CallQueuedHandler->dispatchThroughMiddleware(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(App\\Jobs\\ProcessTextGeneration))
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Jobs\\Job.php(102): Illuminate\\Queue\\CallQueuedHandler->call(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Array)
#15 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(439): Illuminate\\Queue\\Jobs\\Job->fire()
#16 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(389): Illuminate\\Queue\\Worker->process('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Queue\\WorkerOptions))
#17 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(176): Illuminate\\Queue\\Worker->runJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 'database', Object(Illuminate\\Queue\\WorkerOptions))
#18 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(138): Illuminate\\Queue\\Worker->daemon('database', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#19 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(121): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'default')
#20 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#21 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#23 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Laravel\\Lumen\\Application), Array, Object(Closure))
#24 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Laravel\\Lumen\\Application), Array, Array, NULL)
#25 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#26 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\console\\Command.php(181): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#28 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(1078): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Console\\Kernel.php(160): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\artisan(35): Laravel\\Lumen\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 {main}
"} 
[2025-08-13 16:23:44] production.INFO: WebSocket推送进度 - 任务开始 {"task_id":"text_gen_1755071151_RDadrxLR","user_id":14,"progress":10,"message":"开始文本生成任务","push_type":"ai_generation_progress","redis_channel":"websocket:push:session_*","push_method":"Redis通道桥接","timestamp":1755073424,"job_class":"App\\Jobs\\ProcessTextGeneration","job_method":"handle"} 
[2025-08-13 16:23:44] production.WARNING: 未找到任务记录 {"task_id":"text_gen_1755071151_RDadrxLR","user_id":14} 
[2025-08-13 16:23:44] production.INFO: WebSocket推送结果 - 任务开始 {"task_id":"text_gen_1755071151_RDadrxLR","user_id":14,"push_success":"no","push_method":"Redis通道桥接"} 
[2025-08-13 16:23:44] production.INFO: WebSocket推送进度 - 连接AI平台 {"task_id":"text_gen_1755071151_RDadrxLR","user_id":14,"progress":30,"message":"连接AI平台","push_type":"ai_generation_progress","redis_channel":"websocket:push:session_*","push_method":"Redis通道桥接","timestamp":1755073424,"job_class":"App\\Jobs\\ProcessTextGeneration","job_method":"handle"} 
[2025-08-13 16:23:44] production.WARNING: 未找到任务记录 {"task_id":"text_gen_1755071151_RDadrxLR","user_id":14} 
[2025-08-13 16:23:44] production.INFO: WebSocket推送结果 - 连接AI平台 {"task_id":"text_gen_1755071151_RDadrxLR","user_id":14,"push_success":"no","push_method":"Redis通道桥接"} 
[2025-08-13 16:23:44] production.INFO: 从WebSocket会话获取business_type {"task_id":"text_gen_1755071151_RDadrxLR","user_id":14,"session_id":"ws_XeLhDmQCfkQrn6vNY7DA91L0UF3erivH","business_type":"text_generation_storyboard"} 
[2025-08-13 16:23:44] production.DEBUG: Lumen事务开始 {"context":"AiGenerationService.generateText","level":1,"action":"real_begin","framework":"Lumen 10"} 
[2025-08-13 16:23:44] production.INFO: AI服务调用 {"platform":"deepseek","mode":"mock","data_size":118} 
[2025-08-13 16:23:44] production.INFO: 调用模拟服务 {"platform":"deepseek","url":"https://aiapi.tiptop.cn/deepseek/chat/completions","timeout":"30"} 
[2025-08-13 16:23:44] production.INFO: 模拟服务调用成功 {"platform":"deepseek","status":200,"response_size":0} 
[2025-08-13 16:23:44] production.INFO: 用户偏好记录更新成功 {"user_id":14,"platform":"deepseek","task_type":"text_generation_storyboard","usage_count":1,"success_rate":1.0} 
[2025-08-13 16:23:44] production.INFO: 积分消费成功 {"transaction_id":29,"user_id":14,"amount":"0.58"} 
[2025-08-13 16:23:44] production.ERROR: AI文本生成失败，跳过后续处理 {"user_id":14,"task_id":29,"ai_response_code":200,"ai_response_message":"文本生成成功","has_text_content":false} 
[2025-08-13 16:23:44] production.WARNING: 事务回滚 {"context":"AiGenerationService.generateText","reason":"AI文本生成失败","original_level":1,"action":"real_rollback"} 
[2025-08-13 16:23:44] production.INFO: WebSocket推送进度 - 保存文本数据 {"task_id":"text_gen_1755071151_RDadrxLR","user_id":14,"progress":80,"message":"保存文本数据","push_type":"ai_generation_progress","redis_channel":"websocket:push:session_*","push_method":"Redis通道桥接","timestamp":1755073424,"job_class":"App\\Jobs\\ProcessTextGeneration","job_method":"handle"} 
[2025-08-13 16:23:44] production.WARNING: 未找到任务记录 {"task_id":"text_gen_1755071151_RDadrxLR","user_id":14} 
[2025-08-13 16:23:44] production.INFO: WebSocket推送结果 - 保存文本数据 {"task_id":"text_gen_1755071151_RDadrxLR","user_id":14,"push_success":"no","push_method":"Redis通道桥接"} 
[2025-08-13 16:23:44] production.INFO: WebSocket推送进度 - 文本生成完成 {"task_id":"text_gen_1755071151_RDadrxLR","user_id":14,"progress":100,"message":"文本生成完成","push_type":"ai_generation_progress"} 
[2025-08-13 16:23:44] production.WARNING: 未找到任务记录 {"task_id":"text_gen_1755071151_RDadrxLR","user_id":14} 
[2025-08-13 16:23:44] production.INFO: WebSocket推送事件 - 任务完成 {"task_id":"text_gen_1755071151_RDadrxLR","user_id":14,"push_type":"ai_generation_completed","redis_channel":"websocket:push:session_*","push_method":"Redis通道桥接","timestamp":1755073424,"job_class":"App\\Jobs\\ProcessTextGeneration","job_method":"handle"} 
[2025-08-13 16:23:44] production.WARNING: 未找到任务记录 {"task_id":"text_gen_1755071151_RDadrxLR","user_id":14} 
[2025-08-13 16:23:44] production.INFO: WebSocket推送结果 - 任务完成 {"task_id":"text_gen_1755071151_RDadrxLR","user_id":14,"push_success":"no","push_method":"Redis通道桥接"} 
[2025-08-13 16:23:44] production.INFO: 文本生成任务完成 {"task_id":"text_gen_1755071151_RDadrxLR","user_id":14,"context":"text","ai_task_id":29,"cost":0} 
[2025-08-13 16:23:44] production.INFO: WebSocket推送进度 - 任务开始 {"task_id":"text_gen_1755073292_4ZJotDP6","user_id":14,"progress":10,"message":"开始文本生成任务","push_type":"ai_generation_progress","redis_channel":"websocket:push:session_*","push_method":"Redis通道桥接","timestamp":1755073424,"job_class":"App\\Jobs\\ProcessTextGeneration","job_method":"handle"} 
[2025-08-13 16:23:44] production.WARNING: 未找到任务记录 {"task_id":"text_gen_1755073292_4ZJotDP6","user_id":14} 
[2025-08-13 16:23:44] production.INFO: WebSocket推送结果 - 任务开始 {"task_id":"text_gen_1755073292_4ZJotDP6","user_id":14,"push_success":"no","push_method":"Redis通道桥接"} 
[2025-08-13 16:23:44] production.INFO: WebSocket推送进度 - 连接AI平台 {"task_id":"text_gen_1755073292_4ZJotDP6","user_id":14,"progress":30,"message":"连接AI平台","push_type":"ai_generation_progress","redis_channel":"websocket:push:session_*","push_method":"Redis通道桥接","timestamp":1755073424,"job_class":"App\\Jobs\\ProcessTextGeneration","job_method":"handle"} 
[2025-08-13 16:23:44] production.WARNING: 未找到任务记录 {"task_id":"text_gen_1755073292_4ZJotDP6","user_id":14} 
[2025-08-13 16:23:44] production.INFO: WebSocket推送结果 - 连接AI平台 {"task_id":"text_gen_1755073292_4ZJotDP6","user_id":14,"push_success":"no","push_method":"Redis通道桥接"} 
[2025-08-13 16:23:44] production.INFO: 从WebSocket会话获取business_type {"task_id":"text_gen_1755073292_4ZJotDP6","user_id":14,"session_id":"ws_XeLhDmQCfkQrn6vNY7DA91L0UF3erivH","business_type":"text_generation_storyboard"} 
[2025-08-13 16:23:44] production.DEBUG: Lumen事务开始 {"context":"AiGenerationService.generateText","level":1,"action":"real_begin","framework":"Lumen 10"} 
[2025-08-13 16:23:44] production.INFO: AI服务调用 {"platform":"deepseek","mode":"mock","data_size":118} 
[2025-08-13 16:23:44] production.INFO: 调用模拟服务 {"platform":"deepseek","url":"https://aiapi.tiptop.cn/deepseek/chat/completions","timeout":"30"} 
[2025-08-13 16:23:44] production.INFO: 模拟服务调用成功 {"platform":"deepseek","status":200,"response_size":0} 
[2025-08-13 16:23:44] production.INFO: 用户偏好记录更新成功 {"user_id":14,"platform":"deepseek","task_type":"text_generation_storyboard","usage_count":1,"success_rate":1.0} 
[2025-08-13 16:23:44] production.INFO: 积分消费成功 {"transaction_id":30,"user_id":14,"amount":"0.59"} 
[2025-08-13 16:23:44] production.ERROR: AI文本生成失败，跳过后续处理 {"user_id":14,"task_id":30,"ai_response_code":200,"ai_response_message":"文本生成成功","has_text_content":false} 
[2025-08-13 16:23:44] production.WARNING: 事务回滚 {"context":"AiGenerationService.generateText","reason":"AI文本生成失败","original_level":1,"action":"real_rollback"} 
[2025-08-13 16:23:44] production.INFO: WebSocket推送进度 - 保存文本数据 {"task_id":"text_gen_1755073292_4ZJotDP6","user_id":14,"progress":80,"message":"保存文本数据","push_type":"ai_generation_progress","redis_channel":"websocket:push:session_*","push_method":"Redis通道桥接","timestamp":1755073424,"job_class":"App\\Jobs\\ProcessTextGeneration","job_method":"handle"} 
[2025-08-13 16:23:44] production.WARNING: 未找到任务记录 {"task_id":"text_gen_1755073292_4ZJotDP6","user_id":14} 
[2025-08-13 16:23:44] production.INFO: WebSocket推送结果 - 保存文本数据 {"task_id":"text_gen_1755073292_4ZJotDP6","user_id":14,"push_success":"no","push_method":"Redis通道桥接"} 
[2025-08-13 16:23:44] production.INFO: WebSocket推送进度 - 文本生成完成 {"task_id":"text_gen_1755073292_4ZJotDP6","user_id":14,"progress":100,"message":"文本生成完成","push_type":"ai_generation_progress"} 
[2025-08-13 16:23:44] production.WARNING: 未找到任务记录 {"task_id":"text_gen_1755073292_4ZJotDP6","user_id":14} 
[2025-08-13 16:23:44] production.INFO: WebSocket推送事件 - 任务完成 {"task_id":"text_gen_1755073292_4ZJotDP6","user_id":14,"push_type":"ai_generation_completed","redis_channel":"websocket:push:session_*","push_method":"Redis通道桥接","timestamp":1755073424,"job_class":"App\\Jobs\\ProcessTextGeneration","job_method":"handle"} 
[2025-08-13 16:23:44] production.WARNING: 未找到任务记录 {"task_id":"text_gen_1755073292_4ZJotDP6","user_id":14} 
[2025-08-13 16:23:44] production.INFO: WebSocket推送结果 - 任务完成 {"task_id":"text_gen_1755073292_4ZJotDP6","user_id":14,"push_success":"no","push_method":"Redis通道桥接"} 
[2025-08-13 16:23:44] production.INFO: 文本生成任务完成 {"task_id":"text_gen_1755073292_4ZJotDP6","user_id":14,"context":"text","ai_task_id":30,"cost":0} 
[2025-08-13 16:23:44] production.INFO: WebSocket推送进度 - 任务开始 {"task_id":"text_gen_1754973513_flvmG0eH","user_id":13,"progress":10,"message":"开始文本生成任务","push_type":"ai_generation_progress","redis_channel":"websocket:push:session_*","push_method":"Redis通道桥接","timestamp":1755073424,"job_class":"App\\Jobs\\ProcessTextGeneration","job_method":"handle"} 
[2025-08-13 16:23:44] production.WARNING: 未找到任务记录 {"task_id":"text_gen_1754973513_flvmG0eH","user_id":13} 
[2025-08-13 16:23:44] production.INFO: WebSocket推送结果 - 任务开始 {"task_id":"text_gen_1754973513_flvmG0eH","user_id":13,"push_success":"no","push_method":"Redis通道桥接"} 
[2025-08-13 16:23:44] production.INFO: WebSocket推送进度 - 连接AI平台 {"task_id":"text_gen_1754973513_flvmG0eH","user_id":13,"progress":30,"message":"连接AI平台","push_type":"ai_generation_progress","redis_channel":"websocket:push:session_*","push_method":"Redis通道桥接","timestamp":1755073424,"job_class":"App\\Jobs\\ProcessTextGeneration","job_method":"handle"} 
[2025-08-13 16:23:44] production.WARNING: 未找到任务记录 {"task_id":"text_gen_1754973513_flvmG0eH","user_id":13} 
[2025-08-13 16:23:44] production.INFO: WebSocket推送结果 - 连接AI平台 {"task_id":"text_gen_1754973513_flvmG0eH","user_id":13,"push_success":"no","push_method":"Redis通道桥接"} 
[2025-08-13 16:23:44] production.WARNING: 未找到活跃的WebSocket会话或business_type为空 {"task_id":"text_gen_1754973513_flvmG0eH","user_id":13} 
[2025-08-13 16:23:44] production.DEBUG: Lumen事务开始 {"context":"AiGenerationService.generateText","level":1,"action":"real_begin","framework":"Lumen 10"} 
[2025-08-13 16:23:44] production.WARNING: 事务回滚 {"context":"AiGenerationService.generateText","reason":"Undefined array key \"estimated_cost\"","original_level":1,"action":"real_rollback"} 
[2025-08-13 16:23:44] production.ERROR: 文本生成任务创建失败 {"method":"App\\Services\\PyApi\\AiGenerationService::generateText","error_context":{"user_id":13,"prompt":"","model_id":null,"project_id":null,"generation_params_count":3},"error":"Undefined array key \"estimated_cost\"","trace":"#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RegistersExceptionHandlers.php(47): Laravel\\Lumen\\Application->handleError(2, 'Undefined array...', 'D:\\\\longtool\\\\php...', 291)
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\AiGenerationService.php(291): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(2, 'Undefined array...', 'D:\\\\longtool\\\\php...', 291)
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Jobs\\ProcessTextGeneration.php(143): App\\Services\\PyApi\\AiGenerationService->generateText(13, '', NULL, NULL, NULL, Array, NULL, 'text_gen_175497...')
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(36): App\\Jobs\\ProcessTextGeneration->handle()
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Laravel\\Lumen\\Application), Array, Object(Closure))
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Laravel\\Lumen\\Application), Array, Array, NULL)
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\bus\\Dispatcher.php(128): Illuminate\\Container\\Container->call(Array)
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(144): Illuminate\\Bus\\Dispatcher->Illuminate\\Bus\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\bus\\Dispatcher.php(132): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\CallQueuedHandler.php(124): Illuminate\\Bus\\Dispatcher->dispatchNow(Object(App\\Jobs\\ProcessTextGeneration), false)
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(144): Illuminate\\Queue\\CallQueuedHandler->Illuminate\\Queue\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#15 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\CallQueuedHandler.php(126): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#16 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\CallQueuedHandler.php(70): Illuminate\\Queue\\CallQueuedHandler->dispatchThroughMiddleware(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(App\\Jobs\\ProcessTextGeneration))
#17 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Jobs\\Job.php(102): Illuminate\\Queue\\CallQueuedHandler->call(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Array)
#18 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(439): Illuminate\\Queue\\Jobs\\Job->fire()
#19 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(389): Illuminate\\Queue\\Worker->process('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Queue\\WorkerOptions))
#20 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(176): Illuminate\\Queue\\Worker->runJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 'database', Object(Illuminate\\Queue\\WorkerOptions))
#21 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(138): Illuminate\\Queue\\Worker->daemon('database', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#22 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(121): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'default')
#23 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#24 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#25 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#26 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Laravel\\Lumen\\Application), Array, Object(Closure))
#27 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Laravel\\Lumen\\Application), Array, Array, NULL)
#28 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#29 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#30 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\console\\Command.php(181): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#31 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(1078): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#34 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Console\\Kernel.php(160): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#35 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\artisan(35): Laravel\\Lumen\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#36 {main}"} 
[2025-08-13 16:23:44] production.INFO: WebSocket推送事件 - 任务失败 {"task_id":"text_gen_1754973513_flvmG0eH","user_id":13,"error_message":"文本生成任务创建失败","push_type":"ai_generation_failed","redis_channel":"websocket:push:session_*","push_method":"Redis通道桥接","timestamp":1755073424,"job_class":"App\\Jobs\\ProcessTextGeneration","job_method":"handle"} 
[2025-08-13 16:23:44] production.WARNING: 未找到任务记录 {"task_id":"text_gen_1754973513_flvmG0eH","user_id":13} 
[2025-08-13 16:23:44] production.INFO: WebSocket推送结果 - 任务失败 {"task_id":"text_gen_1754973513_flvmG0eH","user_id":13,"push_success":"no","push_method":"Redis通道桥接","error_message":"文本生成任务创建失败"} 
[2025-08-13 16:23:44] production.ERROR: 发布文本生成失败事件异常 {"task_id":"text_gen_1754973513_flvmG0eH","error":"cURL error 60: SSL certificate problem: unable to get local issuer certificate (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.tiptop.cn/py-api/events/publish"} 
[2025-08-13 16:23:44] production.ERROR: 文本生成任务失败 {"task_id":"text_gen_1754973513_flvmG0eH","user_id":13,"context":"prompt_edit","prompt":"写一首关于春天的诗","error":"文本生成任务创建失败","trace":"#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(36): App\\Jobs\\ProcessTextGeneration->handle()
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Laravel\\Lumen\\Application), Array, Object(Closure))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Laravel\\Lumen\\Application), Array, Array, NULL)
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\bus\\Dispatcher.php(128): Illuminate\\Container\\Container->call(Array)
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(144): Illuminate\\Bus\\Dispatcher->Illuminate\\Bus\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\bus\\Dispatcher.php(132): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\CallQueuedHandler.php(124): Illuminate\\Bus\\Dispatcher->dispatchNow(Object(App\\Jobs\\ProcessTextGeneration), false)
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(144): Illuminate\\Queue\\CallQueuedHandler->Illuminate\\Queue\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\CallQueuedHandler.php(126): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\CallQueuedHandler.php(70): Illuminate\\Queue\\CallQueuedHandler->dispatchThroughMiddleware(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(App\\Jobs\\ProcessTextGeneration))
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Jobs\\Job.php(102): Illuminate\\Queue\\CallQueuedHandler->call(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Array)
#15 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(439): Illuminate\\Queue\\Jobs\\Job->fire()
#16 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(389): Illuminate\\Queue\\Worker->process('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Queue\\WorkerOptions))
#17 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(176): Illuminate\\Queue\\Worker->runJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 'database', Object(Illuminate\\Queue\\WorkerOptions))
#18 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(138): Illuminate\\Queue\\Worker->daemon('database', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#19 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(121): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'default')
#20 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#21 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#23 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Laravel\\Lumen\\Application), Array, Object(Closure))
#24 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Laravel\\Lumen\\Application), Array, Array, NULL)
#25 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#26 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\console\\Command.php(181): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#28 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(1078): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Console\\Kernel.php(160): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\artisan(35): Laravel\\Lumen\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 {main}"} 
[2025-08-13 16:23:44] production.ERROR: 文本生成任务创建失败 {"exception":"[object] (Exception(code: 0): 文本生成任务创建失败 at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Jobs\\ProcessTextGeneration.php:226)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(36): App\\Jobs\\ProcessTextGeneration->handle()
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Laravel\\Lumen\\Application), Array, Object(Closure))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Laravel\\Lumen\\Application), Array, Array, NULL)
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\bus\\Dispatcher.php(128): Illuminate\\Container\\Container->call(Array)
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(144): Illuminate\\Bus\\Dispatcher->Illuminate\\Bus\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\bus\\Dispatcher.php(132): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\CallQueuedHandler.php(124): Illuminate\\Bus\\Dispatcher->dispatchNow(Object(App\\Jobs\\ProcessTextGeneration), false)
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(144): Illuminate\\Queue\\CallQueuedHandler->Illuminate\\Queue\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\CallQueuedHandler.php(126): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\CallQueuedHandler.php(70): Illuminate\\Queue\\CallQueuedHandler->dispatchThroughMiddleware(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(App\\Jobs\\ProcessTextGeneration))
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Jobs\\Job.php(102): Illuminate\\Queue\\CallQueuedHandler->call(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Array)
#15 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(439): Illuminate\\Queue\\Jobs\\Job->fire()
#16 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(389): Illuminate\\Queue\\Worker->process('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Queue\\WorkerOptions))
#17 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(176): Illuminate\\Queue\\Worker->runJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 'database', Object(Illuminate\\Queue\\WorkerOptions))
#18 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(138): Illuminate\\Queue\\Worker->daemon('database', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#19 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(121): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'default')
#20 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#21 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#23 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Laravel\\Lumen\\Application), Array, Object(Closure))
#24 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Laravel\\Lumen\\Application), Array, Array, NULL)
#25 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#26 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\console\\Command.php(181): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#28 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(1078): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Console\\Kernel.php(160): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\artisan(35): Laravel\\Lumen\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 {main}
"} 
[2025-08-13 16:23:44] production.INFO: WebSocket推送进度 - 任务开始 {"task_id":"text_gen_1754974116_UKtS5jOP","user_id":13,"progress":10,"message":"开始文本生成任务","push_type":"ai_generation_progress","redis_channel":"websocket:push:session_*","push_method":"Redis通道桥接","timestamp":1755073424,"job_class":"App\\Jobs\\ProcessTextGeneration","job_method":"handle"} 
[2025-08-13 16:23:44] production.WARNING: 未找到任务记录 {"task_id":"text_gen_1754974116_UKtS5jOP","user_id":13} 
[2025-08-13 16:23:44] production.INFO: WebSocket推送结果 - 任务开始 {"task_id":"text_gen_1754974116_UKtS5jOP","user_id":13,"push_success":"no","push_method":"Redis通道桥接"} 
[2025-08-13 16:23:44] production.INFO: WebSocket推送进度 - 连接AI平台 {"task_id":"text_gen_1754974116_UKtS5jOP","user_id":13,"progress":30,"message":"连接AI平台","push_type":"ai_generation_progress","redis_channel":"websocket:push:session_*","push_method":"Redis通道桥接","timestamp":1755073424,"job_class":"App\\Jobs\\ProcessTextGeneration","job_method":"handle"} 
[2025-08-13 16:23:44] production.WARNING: 未找到任务记录 {"task_id":"text_gen_1754974116_UKtS5jOP","user_id":13} 
[2025-08-13 16:23:44] production.INFO: WebSocket推送结果 - 连接AI平台 {"task_id":"text_gen_1754974116_UKtS5jOP","user_id":13,"push_success":"no","push_method":"Redis通道桥接"} 
[2025-08-13 16:23:44] production.WARNING: 未找到活跃的WebSocket会话或business_type为空 {"task_id":"text_gen_1754974116_UKtS5jOP","user_id":13} 
[2025-08-13 16:23:44] production.DEBUG: Lumen事务开始 {"context":"AiGenerationService.generateText","level":1,"action":"real_begin","framework":"Lumen 10"} 
[2025-08-13 16:23:45] production.WARNING: 事务回滚 {"context":"AiGenerationService.generateText","reason":"Undefined array key \"estimated_cost\"","original_level":1,"action":"real_rollback"} 
[2025-08-13 16:23:45] production.ERROR: 文本生成任务创建失败 {"method":"App\\Services\\PyApi\\AiGenerationService::generateText","error_context":{"user_id":13,"prompt":"","model_id":null,"project_id":null,"generation_params_count":3},"error":"Undefined array key \"estimated_cost\"","trace":"#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RegistersExceptionHandlers.php(47): Laravel\\Lumen\\Application->handleError(2, 'Undefined array...', 'D:\\\\longtool\\\\php...', 291)
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\AiGenerationService.php(291): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(2, 'Undefined array...', 'D:\\\\longtool\\\\php...', 291)
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Jobs\\ProcessTextGeneration.php(143): App\\Services\\PyApi\\AiGenerationService->generateText(13, '', NULL, NULL, NULL, Array, NULL, 'text_gen_175497...')
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(36): App\\Jobs\\ProcessTextGeneration->handle()
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Laravel\\Lumen\\Application), Array, Object(Closure))
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Laravel\\Lumen\\Application), Array, Array, NULL)
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\bus\\Dispatcher.php(128): Illuminate\\Container\\Container->call(Array)
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(144): Illuminate\\Bus\\Dispatcher->Illuminate\\Bus\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\bus\\Dispatcher.php(132): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\CallQueuedHandler.php(124): Illuminate\\Bus\\Dispatcher->dispatchNow(Object(App\\Jobs\\ProcessTextGeneration), false)
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(144): Illuminate\\Queue\\CallQueuedHandler->Illuminate\\Queue\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#15 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\CallQueuedHandler.php(126): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#16 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\CallQueuedHandler.php(70): Illuminate\\Queue\\CallQueuedHandler->dispatchThroughMiddleware(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(App\\Jobs\\ProcessTextGeneration))
#17 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Jobs\\Job.php(102): Illuminate\\Queue\\CallQueuedHandler->call(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Array)
#18 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(439): Illuminate\\Queue\\Jobs\\Job->fire()
#19 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(389): Illuminate\\Queue\\Worker->process('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Queue\\WorkerOptions))
#20 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(176): Illuminate\\Queue\\Worker->runJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 'database', Object(Illuminate\\Queue\\WorkerOptions))
#21 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(138): Illuminate\\Queue\\Worker->daemon('database', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#22 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(121): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'default')
#23 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#24 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#25 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#26 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Laravel\\Lumen\\Application), Array, Object(Closure))
#27 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Laravel\\Lumen\\Application), Array, Array, NULL)
#28 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#29 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#30 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\console\\Command.php(181): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#31 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(1078): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#34 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Console\\Kernel.php(160): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#35 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\artisan(35): Laravel\\Lumen\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#36 {main}"} 
[2025-08-13 16:23:45] production.INFO: WebSocket推送事件 - 任务失败 {"task_id":"text_gen_1754974116_UKtS5jOP","user_id":13,"error_message":"文本生成任务创建失败","push_type":"ai_generation_failed","redis_channel":"websocket:push:session_*","push_method":"Redis通道桥接","timestamp":1755073425,"job_class":"App\\Jobs\\ProcessTextGeneration","job_method":"handle"} 
[2025-08-13 16:23:45] production.WARNING: 未找到任务记录 {"task_id":"text_gen_1754974116_UKtS5jOP","user_id":13} 
[2025-08-13 16:23:45] production.INFO: WebSocket推送结果 - 任务失败 {"task_id":"text_gen_1754974116_UKtS5jOP","user_id":13,"push_success":"no","push_method":"Redis通道桥接","error_message":"文本生成任务创建失败"} 
[2025-08-13 16:23:45] production.ERROR: 发布文本生成失败事件异常 {"task_id":"text_gen_1754974116_UKtS5jOP","error":"cURL error 60: SSL certificate problem: unable to get local issuer certificate (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.tiptop.cn/py-api/events/publish"} 
[2025-08-13 16:23:45] production.ERROR: 文本生成任务失败 {"task_id":"text_gen_1754974116_UKtS5jOP","user_id":13,"context":"prompt_edit","prompt":"请写一首关于春天的诗","error":"文本生成任务创建失败","trace":"#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(36): App\\Jobs\\ProcessTextGeneration->handle()
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Laravel\\Lumen\\Application), Array, Object(Closure))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Laravel\\Lumen\\Application), Array, Array, NULL)
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\bus\\Dispatcher.php(128): Illuminate\\Container\\Container->call(Array)
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(144): Illuminate\\Bus\\Dispatcher->Illuminate\\Bus\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\bus\\Dispatcher.php(132): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\CallQueuedHandler.php(124): Illuminate\\Bus\\Dispatcher->dispatchNow(Object(App\\Jobs\\ProcessTextGeneration), false)
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(144): Illuminate\\Queue\\CallQueuedHandler->Illuminate\\Queue\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\CallQueuedHandler.php(126): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\CallQueuedHandler.php(70): Illuminate\\Queue\\CallQueuedHandler->dispatchThroughMiddleware(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(App\\Jobs\\ProcessTextGeneration))
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Jobs\\Job.php(102): Illuminate\\Queue\\CallQueuedHandler->call(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Array)
#15 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(439): Illuminate\\Queue\\Jobs\\Job->fire()
#16 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(389): Illuminate\\Queue\\Worker->process('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Queue\\WorkerOptions))
#17 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(176): Illuminate\\Queue\\Worker->runJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 'database', Object(Illuminate\\Queue\\WorkerOptions))
#18 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(138): Illuminate\\Queue\\Worker->daemon('database', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#19 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(121): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'default')
#20 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#21 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#23 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Laravel\\Lumen\\Application), Array, Object(Closure))
#24 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Laravel\\Lumen\\Application), Array, Array, NULL)
#25 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#26 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\console\\Command.php(181): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#28 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(1078): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Console\\Kernel.php(160): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\artisan(35): Laravel\\Lumen\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 {main}"} 
[2025-08-13 16:23:45] production.ERROR: 文本生成任务创建失败 {"exception":"[object] (Exception(code: 0): 文本生成任务创建失败 at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Jobs\\ProcessTextGeneration.php:226)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(36): App\\Jobs\\ProcessTextGeneration->handle()
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Laravel\\Lumen\\Application), Array, Object(Closure))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Laravel\\Lumen\\Application), Array, Array, NULL)
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\bus\\Dispatcher.php(128): Illuminate\\Container\\Container->call(Array)
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(144): Illuminate\\Bus\\Dispatcher->Illuminate\\Bus\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\bus\\Dispatcher.php(132): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\CallQueuedHandler.php(124): Illuminate\\Bus\\Dispatcher->dispatchNow(Object(App\\Jobs\\ProcessTextGeneration), false)
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(144): Illuminate\\Queue\\CallQueuedHandler->Illuminate\\Queue\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\CallQueuedHandler.php(126): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\CallQueuedHandler.php(70): Illuminate\\Queue\\CallQueuedHandler->dispatchThroughMiddleware(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(App\\Jobs\\ProcessTextGeneration))
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Jobs\\Job.php(102): Illuminate\\Queue\\CallQueuedHandler->call(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Array)
#15 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(439): Illuminate\\Queue\\Jobs\\Job->fire()
#16 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(389): Illuminate\\Queue\\Worker->process('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Queue\\WorkerOptions))
#17 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(176): Illuminate\\Queue\\Worker->runJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 'database', Object(Illuminate\\Queue\\WorkerOptions))
#18 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(138): Illuminate\\Queue\\Worker->daemon('database', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#19 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(121): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'default')
#20 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#21 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#23 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Laravel\\Lumen\\Application), Array, Object(Closure))
#24 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Laravel\\Lumen\\Application), Array, Array, NULL)
#25 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#26 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\console\\Command.php(181): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#28 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(1078): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Console\\Kernel.php(160): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\artisan(35): Laravel\\Lumen\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 {main}
"} 
[2025-08-13 16:23:45] production.INFO: WebSocket推送进度 - 任务开始 {"task_id":"text_gen_1754988026_B98EDyFQ","user_id":13,"progress":10,"message":"开始文本生成任务","push_type":"ai_generation_progress","redis_channel":"websocket:push:session_*","push_method":"Redis通道桥接","timestamp":1755073425,"job_class":"App\\Jobs\\ProcessTextGeneration","job_method":"handle"} 
[2025-08-13 16:23:45] production.WARNING: 未找到任务记录 {"task_id":"text_gen_1754988026_B98EDyFQ","user_id":13} 
[2025-08-13 16:23:45] production.INFO: WebSocket推送结果 - 任务开始 {"task_id":"text_gen_1754988026_B98EDyFQ","user_id":13,"push_success":"no","push_method":"Redis通道桥接"} 
[2025-08-13 16:23:45] production.INFO: WebSocket推送进度 - 连接AI平台 {"task_id":"text_gen_1754988026_B98EDyFQ","user_id":13,"progress":30,"message":"连接AI平台","push_type":"ai_generation_progress","redis_channel":"websocket:push:session_*","push_method":"Redis通道桥接","timestamp":1755073425,"job_class":"App\\Jobs\\ProcessTextGeneration","job_method":"handle"} 
[2025-08-13 16:23:45] production.WARNING: 未找到任务记录 {"task_id":"text_gen_1754988026_B98EDyFQ","user_id":13} 
[2025-08-13 16:23:45] production.INFO: WebSocket推送结果 - 连接AI平台 {"task_id":"text_gen_1754988026_B98EDyFQ","user_id":13,"push_success":"no","push_method":"Redis通道桥接"} 
[2025-08-13 16:23:45] production.WARNING: 未找到活跃的WebSocket会话或business_type为空 {"task_id":"text_gen_1754988026_B98EDyFQ","user_id":13} 
[2025-08-13 16:23:45] production.DEBUG: Lumen事务开始 {"context":"AiGenerationService.generateText","level":1,"action":"real_begin","framework":"Lumen 10"} 
[2025-08-13 16:23:45] production.WARNING: 事务回滚 {"context":"AiGenerationService.generateText","reason":"Undefined array key \"estimated_cost\"","original_level":1,"action":"real_rollback"} 
[2025-08-13 16:23:45] production.ERROR: 文本生成任务创建失败 {"method":"App\\Services\\PyApi\\AiGenerationService::generateText","error_context":{"user_id":13,"prompt":"","model_id":null,"project_id":null,"generation_params_count":3},"error":"Undefined array key \"estimated_cost\"","trace":"#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RegistersExceptionHandlers.php(47): Laravel\\Lumen\\Application->handleError(2, 'Undefined array...', 'D:\\\\longtool\\\\php...', 291)
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\AiGenerationService.php(291): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(2, 'Undefined array...', 'D:\\\\longtool\\\\php...', 291)
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Jobs\\ProcessTextGeneration.php(143): App\\Services\\PyApi\\AiGenerationService->generateText(13, '', NULL, NULL, NULL, Array, NULL, 'text_gen_175498...')
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(36): App\\Jobs\\ProcessTextGeneration->handle()
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Laravel\\Lumen\\Application), Array, Object(Closure))
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Laravel\\Lumen\\Application), Array, Array, NULL)
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\bus\\Dispatcher.php(128): Illuminate\\Container\\Container->call(Array)
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(144): Illuminate\\Bus\\Dispatcher->Illuminate\\Bus\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\bus\\Dispatcher.php(132): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\CallQueuedHandler.php(124): Illuminate\\Bus\\Dispatcher->dispatchNow(Object(App\\Jobs\\ProcessTextGeneration), false)
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(144): Illuminate\\Queue\\CallQueuedHandler->Illuminate\\Queue\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#15 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\CallQueuedHandler.php(126): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#16 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\CallQueuedHandler.php(70): Illuminate\\Queue\\CallQueuedHandler->dispatchThroughMiddleware(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(App\\Jobs\\ProcessTextGeneration))
#17 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Jobs\\Job.php(102): Illuminate\\Queue\\CallQueuedHandler->call(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Array)
#18 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(439): Illuminate\\Queue\\Jobs\\Job->fire()
#19 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(389): Illuminate\\Queue\\Worker->process('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Queue\\WorkerOptions))
#20 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(176): Illuminate\\Queue\\Worker->runJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 'database', Object(Illuminate\\Queue\\WorkerOptions))
#21 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(138): Illuminate\\Queue\\Worker->daemon('database', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#22 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(121): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'default')
#23 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#24 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#25 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#26 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Laravel\\Lumen\\Application), Array, Object(Closure))
#27 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Laravel\\Lumen\\Application), Array, Array, NULL)
#28 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#29 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#30 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\console\\Command.php(181): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#31 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(1078): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#34 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Console\\Kernel.php(160): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#35 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\artisan(35): Laravel\\Lumen\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#36 {main}"} 
[2025-08-13 16:23:45] production.INFO: WebSocket推送事件 - 任务失败 {"task_id":"text_gen_1754988026_B98EDyFQ","user_id":13,"error_message":"文本生成任务创建失败","push_type":"ai_generation_failed","redis_channel":"websocket:push:session_*","push_method":"Redis通道桥接","timestamp":1755073425,"job_class":"App\\Jobs\\ProcessTextGeneration","job_method":"handle"} 
[2025-08-13 16:23:45] production.WARNING: 未找到任务记录 {"task_id":"text_gen_1754988026_B98EDyFQ","user_id":13} 
[2025-08-13 16:23:45] production.INFO: WebSocket推送结果 - 任务失败 {"task_id":"text_gen_1754988026_B98EDyFQ","user_id":13,"push_success":"no","push_method":"Redis通道桥接","error_message":"文本生成任务创建失败"} 
[2025-08-13 16:23:45] production.ERROR: 发布文本生成失败事件异常 {"task_id":"text_gen_1754988026_B98EDyFQ","error":"cURL error 60: SSL certificate problem: unable to get local issuer certificate (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.tiptop.cn/py-api/events/publish"} 
[2025-08-13 16:23:45] production.ERROR: 文本生成任务失败 {"task_id":"text_gen_1754988026_B98EDyFQ","user_id":13,"context":"prompt_edit","prompt":"请写一首关于春天的诗","error":"文本生成任务创建失败","trace":"#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(36): App\\Jobs\\ProcessTextGeneration->handle()
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Laravel\\Lumen\\Application), Array, Object(Closure))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Laravel\\Lumen\\Application), Array, Array, NULL)
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\bus\\Dispatcher.php(128): Illuminate\\Container\\Container->call(Array)
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(144): Illuminate\\Bus\\Dispatcher->Illuminate\\Bus\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\bus\\Dispatcher.php(132): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\CallQueuedHandler.php(124): Illuminate\\Bus\\Dispatcher->dispatchNow(Object(App\\Jobs\\ProcessTextGeneration), false)
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(144): Illuminate\\Queue\\CallQueuedHandler->Illuminate\\Queue\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\CallQueuedHandler.php(126): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\CallQueuedHandler.php(70): Illuminate\\Queue\\CallQueuedHandler->dispatchThroughMiddleware(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(App\\Jobs\\ProcessTextGeneration))
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Jobs\\Job.php(102): Illuminate\\Queue\\CallQueuedHandler->call(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Array)
#15 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(439): Illuminate\\Queue\\Jobs\\Job->fire()
#16 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(389): Illuminate\\Queue\\Worker->process('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Queue\\WorkerOptions))
#17 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(176): Illuminate\\Queue\\Worker->runJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 'database', Object(Illuminate\\Queue\\WorkerOptions))
#18 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(138): Illuminate\\Queue\\Worker->daemon('database', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#19 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(121): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'default')
#20 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#21 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#23 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Laravel\\Lumen\\Application), Array, Object(Closure))
#24 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Laravel\\Lumen\\Application), Array, Array, NULL)
#25 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#26 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\console\\Command.php(181): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#28 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(1078): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Console\\Kernel.php(160): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\artisan(35): Laravel\\Lumen\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 {main}"} 
[2025-08-13 16:23:45] production.ERROR: 文本生成任务创建失败 {"exception":"[object] (Exception(code: 0): 文本生成任务创建失败 at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Jobs\\ProcessTextGeneration.php:226)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(36): App\\Jobs\\ProcessTextGeneration->handle()
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Laravel\\Lumen\\Application), Array, Object(Closure))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Laravel\\Lumen\\Application), Array, Array, NULL)
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\bus\\Dispatcher.php(128): Illuminate\\Container\\Container->call(Array)
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(144): Illuminate\\Bus\\Dispatcher->Illuminate\\Bus\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\bus\\Dispatcher.php(132): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\CallQueuedHandler.php(124): Illuminate\\Bus\\Dispatcher->dispatchNow(Object(App\\Jobs\\ProcessTextGeneration), false)
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(144): Illuminate\\Queue\\CallQueuedHandler->Illuminate\\Queue\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\CallQueuedHandler.php(126): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\CallQueuedHandler.php(70): Illuminate\\Queue\\CallQueuedHandler->dispatchThroughMiddleware(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(App\\Jobs\\ProcessTextGeneration))
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Jobs\\Job.php(102): Illuminate\\Queue\\CallQueuedHandler->call(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Array)
#15 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(439): Illuminate\\Queue\\Jobs\\Job->fire()
#16 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(389): Illuminate\\Queue\\Worker->process('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Queue\\WorkerOptions))
#17 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(176): Illuminate\\Queue\\Worker->runJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 'database', Object(Illuminate\\Queue\\WorkerOptions))
#18 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(138): Illuminate\\Queue\\Worker->daemon('database', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#19 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(121): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'default')
#20 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#21 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#23 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Laravel\\Lumen\\Application), Array, Object(Closure))
#24 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Laravel\\Lumen\\Application), Array, Array, NULL)
#25 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#26 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\console\\Command.php(181): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#28 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(1078): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Console\\Kernel.php(160): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\artisan(35): Laravel\\Lumen\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 {main}
"} 
[2025-08-13 16:23:45] production.INFO: WebSocket推送进度 - 任务开始 {"task_id":"text_gen_1754973513_flvmG0eH","user_id":13,"progress":10,"message":"开始文本生成任务","push_type":"ai_generation_progress","redis_channel":"websocket:push:session_*","push_method":"Redis通道桥接","timestamp":1755073425,"job_class":"App\\Jobs\\ProcessTextGeneration","job_method":"handle"} 
[2025-08-13 16:23:45] production.WARNING: 未找到任务记录 {"task_id":"text_gen_1754973513_flvmG0eH","user_id":13} 
[2025-08-13 16:23:45] production.INFO: WebSocket推送结果 - 任务开始 {"task_id":"text_gen_1754973513_flvmG0eH","user_id":13,"push_success":"no","push_method":"Redis通道桥接"} 
[2025-08-13 16:23:45] production.INFO: WebSocket推送进度 - 连接AI平台 {"task_id":"text_gen_1754973513_flvmG0eH","user_id":13,"progress":30,"message":"连接AI平台","push_type":"ai_generation_progress","redis_channel":"websocket:push:session_*","push_method":"Redis通道桥接","timestamp":1755073425,"job_class":"App\\Jobs\\ProcessTextGeneration","job_method":"handle"} 
[2025-08-13 16:23:45] production.WARNING: 未找到任务记录 {"task_id":"text_gen_1754973513_flvmG0eH","user_id":13} 
[2025-08-13 16:23:45] production.INFO: WebSocket推送结果 - 连接AI平台 {"task_id":"text_gen_1754973513_flvmG0eH","user_id":13,"push_success":"no","push_method":"Redis通道桥接"} 
[2025-08-13 16:23:45] production.WARNING: 未找到活跃的WebSocket会话或business_type为空 {"task_id":"text_gen_1754973513_flvmG0eH","user_id":13} 
[2025-08-13 16:23:45] production.DEBUG: Lumen事务开始 {"context":"AiGenerationService.generateText","level":1,"action":"real_begin","framework":"Lumen 10"} 
[2025-08-13 16:23:45] production.WARNING: 事务回滚 {"context":"AiGenerationService.generateText","reason":"Undefined array key \"estimated_cost\"","original_level":1,"action":"real_rollback"} 
[2025-08-13 16:23:45] production.ERROR: 文本生成任务创建失败 {"method":"App\\Services\\PyApi\\AiGenerationService::generateText","error_context":{"user_id":13,"prompt":"","model_id":null,"project_id":null,"generation_params_count":3},"error":"Undefined array key \"estimated_cost\"","trace":"#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RegistersExceptionHandlers.php(47): Laravel\\Lumen\\Application->handleError(2, 'Undefined array...', 'D:\\\\longtool\\\\php...', 291)
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\AiGenerationService.php(291): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(2, 'Undefined array...', 'D:\\\\longtool\\\\php...', 291)
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Jobs\\ProcessTextGeneration.php(143): App\\Services\\PyApi\\AiGenerationService->generateText(13, '', NULL, NULL, NULL, Array, NULL, 'text_gen_175497...')
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(36): App\\Jobs\\ProcessTextGeneration->handle()
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Laravel\\Lumen\\Application), Array, Object(Closure))
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Laravel\\Lumen\\Application), Array, Array, NULL)
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\bus\\Dispatcher.php(128): Illuminate\\Container\\Container->call(Array)
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(144): Illuminate\\Bus\\Dispatcher->Illuminate\\Bus\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\bus\\Dispatcher.php(132): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\CallQueuedHandler.php(124): Illuminate\\Bus\\Dispatcher->dispatchNow(Object(App\\Jobs\\ProcessTextGeneration), false)
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(144): Illuminate\\Queue\\CallQueuedHandler->Illuminate\\Queue\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#15 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\CallQueuedHandler.php(126): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#16 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\CallQueuedHandler.php(70): Illuminate\\Queue\\CallQueuedHandler->dispatchThroughMiddleware(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(App\\Jobs\\ProcessTextGeneration))
#17 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Jobs\\Job.php(102): Illuminate\\Queue\\CallQueuedHandler->call(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Array)
#18 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(439): Illuminate\\Queue\\Jobs\\Job->fire()
#19 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(389): Illuminate\\Queue\\Worker->process('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Queue\\WorkerOptions))
#20 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(176): Illuminate\\Queue\\Worker->runJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 'database', Object(Illuminate\\Queue\\WorkerOptions))
#21 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(138): Illuminate\\Queue\\Worker->daemon('database', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#22 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(121): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'default')
#23 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#24 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#25 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#26 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Laravel\\Lumen\\Application), Array, Object(Closure))
#27 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Laravel\\Lumen\\Application), Array, Array, NULL)
#28 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#29 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#30 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\console\\Command.php(181): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#31 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(1078): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#34 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Console\\Kernel.php(160): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#35 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\artisan(35): Laravel\\Lumen\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#36 {main}"} 
[2025-08-13 16:23:45] production.INFO: WebSocket推送事件 - 任务失败 {"task_id":"text_gen_1754973513_flvmG0eH","user_id":13,"error_message":"文本生成任务创建失败","push_type":"ai_generation_failed","redis_channel":"websocket:push:session_*","push_method":"Redis通道桥接","timestamp":1755073425,"job_class":"App\\Jobs\\ProcessTextGeneration","job_method":"handle"} 
[2025-08-13 16:23:45] production.WARNING: 未找到任务记录 {"task_id":"text_gen_1754973513_flvmG0eH","user_id":13} 
[2025-08-13 16:23:45] production.INFO: WebSocket推送结果 - 任务失败 {"task_id":"text_gen_1754973513_flvmG0eH","user_id":13,"push_success":"no","push_method":"Redis通道桥接","error_message":"文本生成任务创建失败"} 
[2025-08-13 16:23:45] production.ERROR: 发布文本生成失败事件异常 {"task_id":"text_gen_1754973513_flvmG0eH","error":"cURL error 60: SSL certificate problem: unable to get local issuer certificate (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.tiptop.cn/py-api/events/publish"} 
[2025-08-13 16:23:45] production.ERROR: 文本生成任务失败 {"task_id":"text_gen_1754973513_flvmG0eH","user_id":13,"context":"prompt_edit","prompt":"写一首关于春天的诗","error":"文本生成任务创建失败","trace":"#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(36): App\\Jobs\\ProcessTextGeneration->handle()
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Laravel\\Lumen\\Application), Array, Object(Closure))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Laravel\\Lumen\\Application), Array, Array, NULL)
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\bus\\Dispatcher.php(128): Illuminate\\Container\\Container->call(Array)
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(144): Illuminate\\Bus\\Dispatcher->Illuminate\\Bus\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\bus\\Dispatcher.php(132): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\CallQueuedHandler.php(124): Illuminate\\Bus\\Dispatcher->dispatchNow(Object(App\\Jobs\\ProcessTextGeneration), false)
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(144): Illuminate\\Queue\\CallQueuedHandler->Illuminate\\Queue\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\CallQueuedHandler.php(126): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\CallQueuedHandler.php(70): Illuminate\\Queue\\CallQueuedHandler->dispatchThroughMiddleware(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(App\\Jobs\\ProcessTextGeneration))
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Jobs\\Job.php(102): Illuminate\\Queue\\CallQueuedHandler->call(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Array)
#15 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(439): Illuminate\\Queue\\Jobs\\Job->fire()
#16 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(389): Illuminate\\Queue\\Worker->process('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Queue\\WorkerOptions))
#17 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(176): Illuminate\\Queue\\Worker->runJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 'database', Object(Illuminate\\Queue\\WorkerOptions))
#18 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(138): Illuminate\\Queue\\Worker->daemon('database', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#19 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(121): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'default')
#20 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#21 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#23 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Laravel\\Lumen\\Application), Array, Object(Closure))
#24 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Laravel\\Lumen\\Application), Array, Array, NULL)
#25 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#26 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\console\\Command.php(181): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#28 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(1078): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Console\\Kernel.php(160): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\artisan(35): Laravel\\Lumen\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 {main}"} 
[2025-08-13 16:23:45] production.INFO: WebSocket推送事件 - 任务最终失败 {"task_id":"text_gen_1754973513_flvmG0eH","user_id":13,"error_message":"文本生成任务创建失败","push_type":"ai_generation_failed","context":"failed_method"} 
[2025-08-13 16:23:45] production.WARNING: 未找到任务记录 {"task_id":"text_gen_1754973513_flvmG0eH","user_id":13} 
[2025-08-13 16:23:45] production.ERROR: 发布文本生成失败事件异常 {"task_id":"text_gen_1754973513_flvmG0eH","error":"cURL error 60: SSL certificate problem: unable to get local issuer certificate (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.tiptop.cn/py-api/events/publish"} 
[2025-08-13 16:23:45] production.ERROR: 文本生成任务最终失败 {"task_id":"text_gen_1754973513_flvmG0eH","user_id":13,"context":"prompt_edit","error":"文本生成任务创建失败"} 
[2025-08-13 16:23:45] production.ERROR: SQLSTATE[HY000]: General error: 1364 Field 'uuid' doesn't have a default value (Connection: mysql, SQL: insert into `p_failed_jobs` (`connection`, `queue`, `payload`, `exception`, `failed_at`) values (database, default, {"uuid":"6cba0e80-a013-44da-bc15-44cba9c8fd67","displayName":"App\\Jobs\\ProcessTextGeneration","job":"Illuminate\\Queue\\CallQueuedHandler@call","maxTries":3,"maxExceptions":null,"failOnTimeout":false,"backoff":null,"timeout":180,"retryUntil":null,"data":{"commandName":"App\\Jobs\\ProcessTextGeneration","command":"O:30:\"App\\Jobs\\ProcessTextGeneration\":5:{s:9:\"\u0000*\u0000taskId\";s:28:\"text_gen_1754973513_flvmG0eH\";s:9:\"\u0000*\u0000userId\";i:13;s:9:\"\u0000*\u0000prompt\";s:27:\"写一首关于春天的诗\";s:19:\"\u0000*\u0000generationParams\";a:3:{s:10:\"max_tokens\";i:1000;s:11:\"temperature\";d:0.7;s:5:\"top_p\";d:0.9;}s:10:\"\u0000*\u0000context\";s:11:\"prompt_edit\";}"}}, Exception: 文本生成任务创建失败 in D:\longtool\phpStudy_64\WWW\tool_api\php\api\app\Jobs\ProcessTextGeneration.php:226
Stack trace:
#0 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\container\BoundMethod.php(36): App\Jobs\ProcessTextGeneration->handle()
#1 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\container\Util.php(41): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#2 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\container\BoundMethod.php(93): Illuminate\Container\Util::unwrapIfClosure(Object(Closure))
#3 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\container\BoundMethod.php(37): Illuminate\Container\BoundMethod::callBoundMethod(Object(Laravel\Lumen\Application), Array, Object(Closure))
#4 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\container\Container.php(662): Illuminate\Container\BoundMethod::call(Object(Laravel\Lumen\Application), Array, Array, NULL)
#5 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\bus\Dispatcher.php(128): Illuminate\Container\Container->call(Array)
#6 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\pipeline\Pipeline.php(144): Illuminate\Bus\Dispatcher->Illuminate\Bus\{closure}(Object(App\Jobs\ProcessTextGeneration))
#7 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\pipeline\Pipeline.php(119): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(App\Jobs\ProcessTextGeneration))
#8 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\bus\Dispatcher.php(132): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#9 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\queue\CallQueuedHandler.php(124): Illuminate\Bus\Dispatcher->dispatchNow(Object(App\Jobs\ProcessTextGeneration), false)
#10 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\pipeline\Pipeline.php(144): Illuminate\Queue\CallQueuedHandler->Illuminate\Queue\{closure}(Object(App\Jobs\ProcessTextGeneration))
#11 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\pipeline\Pipeline.php(119): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(App\Jobs\ProcessTextGeneration))
#12 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\queue\CallQueuedHandler.php(126): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#13 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\queue\CallQueuedHandler.php(70): Illuminate\Queue\CallQueuedHandler->dispatchThroughMiddleware(Object(Illuminate\Queue\Jobs\DatabaseJob), Object(App\Jobs\ProcessTextGeneration))
#14 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\queue\Jobs\Job.php(102): Illuminate\Queue\CallQueuedHandler->call(Object(Illuminate\Queue\Jobs\DatabaseJob), Array)
#15 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\queue\Worker.php(439): Illuminate\Queue\Jobs\Job->fire()
#16 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\queue\Worker.php(389): Illuminate\Queue\Worker->process('database', Object(Illuminate\Queue\Jobs\DatabaseJob), Object(Illuminate\Queue\WorkerOptions))
#17 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\queue\Worker.php(176): Illuminate\Queue\Worker->runJob(Object(Illuminate\Queue\Jobs\DatabaseJob), 'database', Object(Illuminate\Queue\WorkerOptions))
#18 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\queue\Console\WorkCommand.php(138): Illuminate\Queue\Worker->daemon('database', 'default', Object(Illuminate\Queue\WorkerOptions))
#19 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\queue\Console\WorkCommand.php(121): Illuminate\Queue\Console\WorkCommand->runWorker('database', 'default')
#20 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\container\BoundMethod.php(36): Illuminate\Queue\Console\WorkCommand->handle()
#21 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\container\Util.php(41): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#22 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\container\BoundMethod.php(93): Illuminate\Container\Util::unwrapIfClosure(Object(Closure))
#23 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\container\BoundMethod.php(37): Illuminate\Container\BoundMethod::callBoundMethod(Object(Laravel\Lumen\Application), Array, Object(Closure))
#24 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\container\Container.php(662): Illuminate\Container\BoundMethod::call(Object(Laravel\Lumen\Application), Array, Array, NULL)
#25 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\console\Command.php(211): Illuminate\Container\Container->call(Array)
#26 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\symfony\console\Command\Command.php(326): Illuminate\Console\Command->execute(Object(Symfony\Component\Console\Input\ArgvInput), Object(Illuminate\Console\OutputStyle))
#27 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\console\Command.php(181): Symfony\Component\Console\Command\Command->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Illuminate\Console\OutputStyle))
#28 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\symfony\console\Application.php(1078): Illuminate\Console\Command->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#29 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\symfony\console\Application.php(324): Symfony\Component\Console\Application->doRunCommand(Object(Illuminate\Queue\Console\WorkCommand), Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#30 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\symfony\console\Application.php(175): Symfony\Component\Console\Application->doRun(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#31 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\laravel\lumen-framework\src\Console\Kernel.php(160): Symfony\Component\Console\Application->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#32 D:\longtool\phpStudy_64\WWW\tool_api\php\api\artisan(35): Laravel\Lumen\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#33 {main}, 2025-08-13 16:23:45)) {"exception":"[object] (Illuminate\\Database\\QueryException(code: HY000): SQLSTATE[HY000]: General error: 1364 Field 'uuid' doesn't have a default value (Connection: mysql, SQL: insert into `p_failed_jobs` (`connection`, `queue`, `payload`, `exception`, `failed_at`) values (database, default, {\"uuid\":\"6cba0e80-a013-44da-bc15-44cba9c8fd67\",\"displayName\":\"App\\\\Jobs\\\\ProcessTextGeneration\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":3,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":180,\"retryUntil\":null,\"data\":{\"commandName\":\"App\\\\Jobs\\\\ProcessTextGeneration\",\"command\":\"O:30:\\\"App\\\\Jobs\\\\ProcessTextGeneration\\\":5:{s:9:\\\"\\u0000*\\u0000taskId\\\";s:28:\\\"text_gen_1754973513_flvmG0eH\\\";s:9:\\\"\\u0000*\\u0000userId\\\";i:13;s:9:\\\"\\u0000*\\u0000prompt\\\";s:27:\\\"写一首关于春天的诗\\\";s:19:\\\"\\u0000*\\u0000generationParams\\\";a:3:{s:10:\\\"max_tokens\\\";i:1000;s:11:\\\"temperature\\\";d:0.7;s:5:\\\"top_p\\\";d:0.9;}s:10:\\\"\\u0000*\\u0000context\\\";s:11:\\\"prompt_edit\\\";}\"}}, Exception: 文本生成任务创建失败 in D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Jobs\\ProcessTextGeneration.php:226
Stack trace:
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(36): App\\Jobs\\ProcessTextGeneration->handle()
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Laravel\\Lumen\\Application), Array, Object(Closure))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Laravel\\Lumen\\Application), Array, Array, NULL)
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\bus\\Dispatcher.php(128): Illuminate\\Container\\Container->call(Array)
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(144): Illuminate\\Bus\\Dispatcher->Illuminate\\Bus\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\bus\\Dispatcher.php(132): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\CallQueuedHandler.php(124): Illuminate\\Bus\\Dispatcher->dispatchNow(Object(App\\Jobs\\ProcessTextGeneration), false)
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(144): Illuminate\\Queue\\CallQueuedHandler->Illuminate\\Queue\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\CallQueuedHandler.php(126): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\CallQueuedHandler.php(70): Illuminate\\Queue\\CallQueuedHandler->dispatchThroughMiddleware(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(App\\Jobs\\ProcessTextGeneration))
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Jobs\\Job.php(102): Illuminate\\Queue\\CallQueuedHandler->call(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Array)
#15 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(439): Illuminate\\Queue\\Jobs\\Job->fire()
#16 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(389): Illuminate\\Queue\\Worker->process('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Queue\\WorkerOptions))
#17 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(176): Illuminate\\Queue\\Worker->runJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 'database', Object(Illuminate\\Queue\\WorkerOptions))
#18 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(138): Illuminate\\Queue\\Worker->daemon('database', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#19 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(121): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'default')
#20 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#21 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#23 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Laravel\\Lumen\\Application), Array, Object(Closure))
#24 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Laravel\\Lumen\\Application), Array, Array, NULL)
#25 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#26 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\console\\Command.php(181): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#28 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(1078): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Console\\Kernel.php(160): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\artisan(35): Laravel\\Lumen\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 {main}, 2025-08-13 16:23:45)) at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Connection.php:822)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('insert into `p_...', Array, Object(Closure))
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Connection.php(581): Illuminate\\Database\\Connection->run('insert into `p_...', Array, Object(Closure))
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Connection.php(533): Illuminate\\Database\\Connection->statement('insert into `p_...', Array)
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Query\\Processors\\Processor.php(32): Illuminate\\Database\\Connection->insert('insert into `p_...', Array)
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Query\\Builder.php(3387): Illuminate\\Database\\Query\\Processors\\Processor->processInsertGetId(Object(Illuminate\\Database\\Query\\Builder), 'insert into `p_...', Array, NULL)
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Failed\\DatabaseFailedJobProvider.php(63): Illuminate\\Database\\Query\\Builder->insertGetId(Array)
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(280): Illuminate\\Queue\\Failed\\DatabaseFailedJobProvider->log('database', 'default', '{\"uuid\":\"6cba0e...', 'Exception: \\xE6\\x96\\x87\\xE6...')
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(186): Illuminate\\Queue\\Console\\WorkCommand->logFailedJob(Object(Illuminate\\Queue\\Events\\JobFailed))
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\events\\Dispatcher.php(458): Illuminate\\Queue\\Console\\WorkCommand->Illuminate\\Queue\\Console\\{closure}(Object(Illuminate\\Queue\\Events\\JobFailed))
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\events\\Dispatcher.php(286): Illuminate\\Events\\Dispatcher->Illuminate\\Events\\{closure}('Illuminate\\\\Queu...', Array)
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\events\\Dispatcher.php(266): Illuminate\\Events\\Dispatcher->invokeListeners('Illuminate\\\\Queu...', Array, false)
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Jobs\\Job.php(218): Illuminate\\Events\\Dispatcher->dispatch('Illuminate\\\\Queu...')
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(596): Illuminate\\Queue\\Jobs\\Job->fail(Object(Exception))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(542): Illuminate\\Queue\\Worker->failJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Exception))
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(466): Illuminate\\Queue\\Worker->markJobAsFailedIfWillExceedMaxAttempts('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 3, Object(Exception))
#15 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(443): Illuminate\\Queue\\Worker->handleJobException('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Queue\\WorkerOptions), Object(Exception))
#16 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(389): Illuminate\\Queue\\Worker->process('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Queue\\WorkerOptions))
#17 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(176): Illuminate\\Queue\\Worker->runJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 'database', Object(Illuminate\\Queue\\WorkerOptions))
#18 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(138): Illuminate\\Queue\\Worker->daemon('database', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#19 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(121): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'default')
#20 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#21 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#23 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Laravel\\Lumen\\Application), Array, Object(Closure))
#24 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Laravel\\Lumen\\Application), Array, Array, NULL)
#25 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#26 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\console\\Command.php(181): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#28 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(1078): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Console\\Kernel.php(160): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\artisan(35): Laravel\\Lumen\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 {main}

[previous exception] [object] (PDOException(code: HY000): SQLSTATE[HY000]: General error: 1364 Field 'uuid' doesn't have a default value at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Connection.php:580)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Connection.php(580): PDOStatement->execute()
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('insert into `p_...', Array)
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('insert into `p_...', Array, Object(Closure))
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Connection.php(581): Illuminate\\Database\\Connection->run('insert into `p_...', Array, Object(Closure))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Connection.php(533): Illuminate\\Database\\Connection->statement('insert into `p_...', Array)
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Query\\Processors\\Processor.php(32): Illuminate\\Database\\Connection->insert('insert into `p_...', Array)
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Query\\Builder.php(3387): Illuminate\\Database\\Query\\Processors\\Processor->processInsertGetId(Object(Illuminate\\Database\\Query\\Builder), 'insert into `p_...', Array, NULL)
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Failed\\DatabaseFailedJobProvider.php(63): Illuminate\\Database\\Query\\Builder->insertGetId(Array)
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(280): Illuminate\\Queue\\Failed\\DatabaseFailedJobProvider->log('database', 'default', '{\"uuid\":\"6cba0e...', 'Exception: \\xE6\\x96\\x87\\xE6...')
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(186): Illuminate\\Queue\\Console\\WorkCommand->logFailedJob(Object(Illuminate\\Queue\\Events\\JobFailed))
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\events\\Dispatcher.php(458): Illuminate\\Queue\\Console\\WorkCommand->Illuminate\\Queue\\Console\\{closure}(Object(Illuminate\\Queue\\Events\\JobFailed))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\events\\Dispatcher.php(286): Illuminate\\Events\\Dispatcher->Illuminate\\Events\\{closure}('Illuminate\\\\Queu...', Array)
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\events\\Dispatcher.php(266): Illuminate\\Events\\Dispatcher->invokeListeners('Illuminate\\\\Queu...', Array, false)
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Jobs\\Job.php(218): Illuminate\\Events\\Dispatcher->dispatch('Illuminate\\\\Queu...')
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(596): Illuminate\\Queue\\Jobs\\Job->fail(Object(Exception))
#15 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(542): Illuminate\\Queue\\Worker->failJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Exception))
#16 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(466): Illuminate\\Queue\\Worker->markJobAsFailedIfWillExceedMaxAttempts('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 3, Object(Exception))
#17 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(443): Illuminate\\Queue\\Worker->handleJobException('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Queue\\WorkerOptions), Object(Exception))
#18 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(389): Illuminate\\Queue\\Worker->process('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Queue\\WorkerOptions))
#19 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(176): Illuminate\\Queue\\Worker->runJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 'database', Object(Illuminate\\Queue\\WorkerOptions))
#20 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(138): Illuminate\\Queue\\Worker->daemon('database', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#21 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(121): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'default')
#22 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#23 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#24 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#25 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Laravel\\Lumen\\Application), Array, Object(Closure))
#26 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Laravel\\Lumen\\Application), Array, Array, NULL)
#27 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#28 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#29 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\console\\Command.php(181): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#30 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(1078): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Console\\Kernel.php(160): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#34 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\artisan(35): Laravel\\Lumen\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#35 {main}
"} 
[2025-08-13 16:23:45] production.INFO: WebSocket推送进度 - 任务开始 {"task_id":"text_gen_1754974116_UKtS5jOP","user_id":13,"progress":10,"message":"开始文本生成任务","push_type":"ai_generation_progress","redis_channel":"websocket:push:session_*","push_method":"Redis通道桥接","timestamp":1755073425,"job_class":"App\\Jobs\\ProcessTextGeneration","job_method":"handle"} 
[2025-08-13 16:23:45] production.WARNING: 未找到任务记录 {"task_id":"text_gen_1754974116_UKtS5jOP","user_id":13} 
[2025-08-13 16:23:45] production.INFO: WebSocket推送结果 - 任务开始 {"task_id":"text_gen_1754974116_UKtS5jOP","user_id":13,"push_success":"no","push_method":"Redis通道桥接"} 
[2025-08-13 16:23:45] production.INFO: WebSocket推送进度 - 连接AI平台 {"task_id":"text_gen_1754974116_UKtS5jOP","user_id":13,"progress":30,"message":"连接AI平台","push_type":"ai_generation_progress","redis_channel":"websocket:push:session_*","push_method":"Redis通道桥接","timestamp":1755073425,"job_class":"App\\Jobs\\ProcessTextGeneration","job_method":"handle"} 
[2025-08-13 16:23:45] production.WARNING: 未找到任务记录 {"task_id":"text_gen_1754974116_UKtS5jOP","user_id":13} 
[2025-08-13 16:23:45] production.INFO: WebSocket推送结果 - 连接AI平台 {"task_id":"text_gen_1754974116_UKtS5jOP","user_id":13,"push_success":"no","push_method":"Redis通道桥接"} 
[2025-08-13 16:23:45] production.WARNING: 未找到活跃的WebSocket会话或business_type为空 {"task_id":"text_gen_1754974116_UKtS5jOP","user_id":13} 
[2025-08-13 16:23:45] production.DEBUG: Lumen事务开始 {"context":"AiGenerationService.generateText","level":1,"action":"real_begin","framework":"Lumen 10"} 
[2025-08-13 16:23:45] production.WARNING: 事务回滚 {"context":"AiGenerationService.generateText","reason":"Undefined array key \"estimated_cost\"","original_level":1,"action":"real_rollback"} 
[2025-08-13 16:23:45] production.ERROR: 文本生成任务创建失败 {"method":"App\\Services\\PyApi\\AiGenerationService::generateText","error_context":{"user_id":13,"prompt":"","model_id":null,"project_id":null,"generation_params_count":3},"error":"Undefined array key \"estimated_cost\"","trace":"#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RegistersExceptionHandlers.php(47): Laravel\\Lumen\\Application->handleError(2, 'Undefined array...', 'D:\\\\longtool\\\\php...', 291)
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\AiGenerationService.php(291): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(2, 'Undefined array...', 'D:\\\\longtool\\\\php...', 291)
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Jobs\\ProcessTextGeneration.php(143): App\\Services\\PyApi\\AiGenerationService->generateText(13, '', NULL, NULL, NULL, Array, NULL, 'text_gen_175497...')
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(36): App\\Jobs\\ProcessTextGeneration->handle()
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Laravel\\Lumen\\Application), Array, Object(Closure))
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Laravel\\Lumen\\Application), Array, Array, NULL)
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\bus\\Dispatcher.php(128): Illuminate\\Container\\Container->call(Array)
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(144): Illuminate\\Bus\\Dispatcher->Illuminate\\Bus\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\bus\\Dispatcher.php(132): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\CallQueuedHandler.php(124): Illuminate\\Bus\\Dispatcher->dispatchNow(Object(App\\Jobs\\ProcessTextGeneration), false)
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(144): Illuminate\\Queue\\CallQueuedHandler->Illuminate\\Queue\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#15 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\CallQueuedHandler.php(126): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#16 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\CallQueuedHandler.php(70): Illuminate\\Queue\\CallQueuedHandler->dispatchThroughMiddleware(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(App\\Jobs\\ProcessTextGeneration))
#17 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Jobs\\Job.php(102): Illuminate\\Queue\\CallQueuedHandler->call(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Array)
#18 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(439): Illuminate\\Queue\\Jobs\\Job->fire()
#19 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(389): Illuminate\\Queue\\Worker->process('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Queue\\WorkerOptions))
#20 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(176): Illuminate\\Queue\\Worker->runJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 'database', Object(Illuminate\\Queue\\WorkerOptions))
#21 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(138): Illuminate\\Queue\\Worker->daemon('database', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#22 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(121): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'default')
#23 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#24 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#25 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#26 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Laravel\\Lumen\\Application), Array, Object(Closure))
#27 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Laravel\\Lumen\\Application), Array, Array, NULL)
#28 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#29 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#30 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\console\\Command.php(181): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#31 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(1078): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#34 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Console\\Kernel.php(160): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#35 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\artisan(35): Laravel\\Lumen\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#36 {main}"} 
[2025-08-13 16:23:45] production.INFO: WebSocket推送事件 - 任务失败 {"task_id":"text_gen_1754974116_UKtS5jOP","user_id":13,"error_message":"文本生成任务创建失败","push_type":"ai_generation_failed","redis_channel":"websocket:push:session_*","push_method":"Redis通道桥接","timestamp":1755073425,"job_class":"App\\Jobs\\ProcessTextGeneration","job_method":"handle"} 
[2025-08-13 16:23:45] production.WARNING: 未找到任务记录 {"task_id":"text_gen_1754974116_UKtS5jOP","user_id":13} 
[2025-08-13 16:23:45] production.INFO: WebSocket推送结果 - 任务失败 {"task_id":"text_gen_1754974116_UKtS5jOP","user_id":13,"push_success":"no","push_method":"Redis通道桥接","error_message":"文本生成任务创建失败"} 
[2025-08-13 16:23:45] production.ERROR: 发布文本生成失败事件异常 {"task_id":"text_gen_1754974116_UKtS5jOP","error":"cURL error 60: SSL certificate problem: unable to get local issuer certificate (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.tiptop.cn/py-api/events/publish"} 
[2025-08-13 16:23:45] production.ERROR: 文本生成任务失败 {"task_id":"text_gen_1754974116_UKtS5jOP","user_id":13,"context":"prompt_edit","prompt":"请写一首关于春天的诗","error":"文本生成任务创建失败","trace":"#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(36): App\\Jobs\\ProcessTextGeneration->handle()
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Laravel\\Lumen\\Application), Array, Object(Closure))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Laravel\\Lumen\\Application), Array, Array, NULL)
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\bus\\Dispatcher.php(128): Illuminate\\Container\\Container->call(Array)
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(144): Illuminate\\Bus\\Dispatcher->Illuminate\\Bus\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\bus\\Dispatcher.php(132): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\CallQueuedHandler.php(124): Illuminate\\Bus\\Dispatcher->dispatchNow(Object(App\\Jobs\\ProcessTextGeneration), false)
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(144): Illuminate\\Queue\\CallQueuedHandler->Illuminate\\Queue\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\CallQueuedHandler.php(126): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\CallQueuedHandler.php(70): Illuminate\\Queue\\CallQueuedHandler->dispatchThroughMiddleware(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(App\\Jobs\\ProcessTextGeneration))
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Jobs\\Job.php(102): Illuminate\\Queue\\CallQueuedHandler->call(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Array)
#15 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(439): Illuminate\\Queue\\Jobs\\Job->fire()
#16 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(389): Illuminate\\Queue\\Worker->process('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Queue\\WorkerOptions))
#17 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(176): Illuminate\\Queue\\Worker->runJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 'database', Object(Illuminate\\Queue\\WorkerOptions))
#18 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(138): Illuminate\\Queue\\Worker->daemon('database', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#19 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(121): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'default')
#20 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#21 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#23 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Laravel\\Lumen\\Application), Array, Object(Closure))
#24 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Laravel\\Lumen\\Application), Array, Array, NULL)
#25 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#26 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\console\\Command.php(181): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#28 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(1078): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Console\\Kernel.php(160): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\artisan(35): Laravel\\Lumen\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 {main}"} 
[2025-08-13 16:23:45] production.INFO: WebSocket推送事件 - 任务最终失败 {"task_id":"text_gen_1754974116_UKtS5jOP","user_id":13,"error_message":"文本生成任务创建失败","push_type":"ai_generation_failed","context":"failed_method"} 
[2025-08-13 16:23:45] production.WARNING: 未找到任务记录 {"task_id":"text_gen_1754974116_UKtS5jOP","user_id":13} 
[2025-08-13 16:23:45] production.ERROR: 发布文本生成失败事件异常 {"task_id":"text_gen_1754974116_UKtS5jOP","error":"cURL error 60: SSL certificate problem: unable to get local issuer certificate (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.tiptop.cn/py-api/events/publish"} 
[2025-08-13 16:23:45] production.ERROR: 文本生成任务最终失败 {"task_id":"text_gen_1754974116_UKtS5jOP","user_id":13,"context":"prompt_edit","error":"文本生成任务创建失败"} 
[2025-08-13 16:23:45] production.ERROR: SQLSTATE[HY000]: General error: 1364 Field 'uuid' doesn't have a default value (Connection: mysql, SQL: insert into `p_failed_jobs` (`connection`, `queue`, `payload`, `exception`, `failed_at`) values (database, default, {"uuid":"77fad70b-00d9-46cd-ba93-642e3e7dab74","displayName":"App\\Jobs\\ProcessTextGeneration","job":"Illuminate\\Queue\\CallQueuedHandler@call","maxTries":3,"maxExceptions":null,"failOnTimeout":false,"backoff":null,"timeout":180,"retryUntil":null,"data":{"commandName":"App\\Jobs\\ProcessTextGeneration","command":"O:30:\"App\\Jobs\\ProcessTextGeneration\":5:{s:9:\"\u0000*\u0000taskId\";s:28:\"text_gen_1754974116_UKtS5jOP\";s:9:\"\u0000*\u0000userId\";i:13;s:9:\"\u0000*\u0000prompt\";s:30:\"请写一首关于春天的诗\";s:19:\"\u0000*\u0000generationParams\";a:3:{s:10:\"max_tokens\";i:1000;s:11:\"temperature\";d:0.7;s:5:\"top_p\";d:0.9;}s:10:\"\u0000*\u0000context\";s:11:\"prompt_edit\";}"}}, Exception: 文本生成任务创建失败 in D:\longtool\phpStudy_64\WWW\tool_api\php\api\app\Jobs\ProcessTextGeneration.php:226
Stack trace:
#0 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\container\BoundMethod.php(36): App\Jobs\ProcessTextGeneration->handle()
#1 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\container\Util.php(41): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#2 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\container\BoundMethod.php(93): Illuminate\Container\Util::unwrapIfClosure(Object(Closure))
#3 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\container\BoundMethod.php(37): Illuminate\Container\BoundMethod::callBoundMethod(Object(Laravel\Lumen\Application), Array, Object(Closure))
#4 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\container\Container.php(662): Illuminate\Container\BoundMethod::call(Object(Laravel\Lumen\Application), Array, Array, NULL)
#5 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\bus\Dispatcher.php(128): Illuminate\Container\Container->call(Array)
#6 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\pipeline\Pipeline.php(144): Illuminate\Bus\Dispatcher->Illuminate\Bus\{closure}(Object(App\Jobs\ProcessTextGeneration))
#7 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\pipeline\Pipeline.php(119): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(App\Jobs\ProcessTextGeneration))
#8 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\bus\Dispatcher.php(132): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#9 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\queue\CallQueuedHandler.php(124): Illuminate\Bus\Dispatcher->dispatchNow(Object(App\Jobs\ProcessTextGeneration), false)
#10 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\pipeline\Pipeline.php(144): Illuminate\Queue\CallQueuedHandler->Illuminate\Queue\{closure}(Object(App\Jobs\ProcessTextGeneration))
#11 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\pipeline\Pipeline.php(119): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(App\Jobs\ProcessTextGeneration))
#12 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\queue\CallQueuedHandler.php(126): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#13 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\queue\CallQueuedHandler.php(70): Illuminate\Queue\CallQueuedHandler->dispatchThroughMiddleware(Object(Illuminate\Queue\Jobs\DatabaseJob), Object(App\Jobs\ProcessTextGeneration))
#14 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\queue\Jobs\Job.php(102): Illuminate\Queue\CallQueuedHandler->call(Object(Illuminate\Queue\Jobs\DatabaseJob), Array)
#15 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\queue\Worker.php(439): Illuminate\Queue\Jobs\Job->fire()
#16 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\queue\Worker.php(389): Illuminate\Queue\Worker->process('database', Object(Illuminate\Queue\Jobs\DatabaseJob), Object(Illuminate\Queue\WorkerOptions))
#17 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\queue\Worker.php(176): Illuminate\Queue\Worker->runJob(Object(Illuminate\Queue\Jobs\DatabaseJob), 'database', Object(Illuminate\Queue\WorkerOptions))
#18 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\queue\Console\WorkCommand.php(138): Illuminate\Queue\Worker->daemon('database', 'default', Object(Illuminate\Queue\WorkerOptions))
#19 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\queue\Console\WorkCommand.php(121): Illuminate\Queue\Console\WorkCommand->runWorker('database', 'default')
#20 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\container\BoundMethod.php(36): Illuminate\Queue\Console\WorkCommand->handle()
#21 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\container\Util.php(41): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#22 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\container\BoundMethod.php(93): Illuminate\Container\Util::unwrapIfClosure(Object(Closure))
#23 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\container\BoundMethod.php(37): Illuminate\Container\BoundMethod::callBoundMethod(Object(Laravel\Lumen\Application), Array, Object(Closure))
#24 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\container\Container.php(662): Illuminate\Container\BoundMethod::call(Object(Laravel\Lumen\Application), Array, Array, NULL)
#25 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\console\Command.php(211): Illuminate\Container\Container->call(Array)
#26 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\symfony\console\Command\Command.php(326): Illuminate\Console\Command->execute(Object(Symfony\Component\Console\Input\ArgvInput), Object(Illuminate\Console\OutputStyle))
#27 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\console\Command.php(181): Symfony\Component\Console\Command\Command->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Illuminate\Console\OutputStyle))
#28 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\symfony\console\Application.php(1078): Illuminate\Console\Command->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#29 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\symfony\console\Application.php(324): Symfony\Component\Console\Application->doRunCommand(Object(Illuminate\Queue\Console\WorkCommand), Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#30 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\symfony\console\Application.php(175): Symfony\Component\Console\Application->doRun(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#31 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\laravel\lumen-framework\src\Console\Kernel.php(160): Symfony\Component\Console\Application->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#32 D:\longtool\phpStudy_64\WWW\tool_api\php\api\artisan(35): Laravel\Lumen\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#33 {main}, 2025-08-13 16:23:45)) {"exception":"[object] (Illuminate\\Database\\QueryException(code: HY000): SQLSTATE[HY000]: General error: 1364 Field 'uuid' doesn't have a default value (Connection: mysql, SQL: insert into `p_failed_jobs` (`connection`, `queue`, `payload`, `exception`, `failed_at`) values (database, default, {\"uuid\":\"77fad70b-00d9-46cd-ba93-642e3e7dab74\",\"displayName\":\"App\\\\Jobs\\\\ProcessTextGeneration\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":3,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":180,\"retryUntil\":null,\"data\":{\"commandName\":\"App\\\\Jobs\\\\ProcessTextGeneration\",\"command\":\"O:30:\\\"App\\\\Jobs\\\\ProcessTextGeneration\\\":5:{s:9:\\\"\\u0000*\\u0000taskId\\\";s:28:\\\"text_gen_1754974116_UKtS5jOP\\\";s:9:\\\"\\u0000*\\u0000userId\\\";i:13;s:9:\\\"\\u0000*\\u0000prompt\\\";s:30:\\\"请写一首关于春天的诗\\\";s:19:\\\"\\u0000*\\u0000generationParams\\\";a:3:{s:10:\\\"max_tokens\\\";i:1000;s:11:\\\"temperature\\\";d:0.7;s:5:\\\"top_p\\\";d:0.9;}s:10:\\\"\\u0000*\\u0000context\\\";s:11:\\\"prompt_edit\\\";}\"}}, Exception: 文本生成任务创建失败 in D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Jobs\\ProcessTextGeneration.php:226
Stack trace:
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(36): App\\Jobs\\ProcessTextGeneration->handle()
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Laravel\\Lumen\\Application), Array, Object(Closure))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Laravel\\Lumen\\Application), Array, Array, NULL)
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\bus\\Dispatcher.php(128): Illuminate\\Container\\Container->call(Array)
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(144): Illuminate\\Bus\\Dispatcher->Illuminate\\Bus\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\bus\\Dispatcher.php(132): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\CallQueuedHandler.php(124): Illuminate\\Bus\\Dispatcher->dispatchNow(Object(App\\Jobs\\ProcessTextGeneration), false)
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(144): Illuminate\\Queue\\CallQueuedHandler->Illuminate\\Queue\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\CallQueuedHandler.php(126): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\CallQueuedHandler.php(70): Illuminate\\Queue\\CallQueuedHandler->dispatchThroughMiddleware(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(App\\Jobs\\ProcessTextGeneration))
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Jobs\\Job.php(102): Illuminate\\Queue\\CallQueuedHandler->call(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Array)
#15 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(439): Illuminate\\Queue\\Jobs\\Job->fire()
#16 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(389): Illuminate\\Queue\\Worker->process('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Queue\\WorkerOptions))
#17 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(176): Illuminate\\Queue\\Worker->runJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 'database', Object(Illuminate\\Queue\\WorkerOptions))
#18 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(138): Illuminate\\Queue\\Worker->daemon('database', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#19 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(121): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'default')
#20 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#21 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#23 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Laravel\\Lumen\\Application), Array, Object(Closure))
#24 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Laravel\\Lumen\\Application), Array, Array, NULL)
#25 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#26 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\console\\Command.php(181): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#28 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(1078): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Console\\Kernel.php(160): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\artisan(35): Laravel\\Lumen\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 {main}, 2025-08-13 16:23:45)) at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Connection.php:822)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('insert into `p_...', Array, Object(Closure))
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Connection.php(581): Illuminate\\Database\\Connection->run('insert into `p_...', Array, Object(Closure))
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Connection.php(533): Illuminate\\Database\\Connection->statement('insert into `p_...', Array)
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Query\\Processors\\Processor.php(32): Illuminate\\Database\\Connection->insert('insert into `p_...', Array)
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Query\\Builder.php(3387): Illuminate\\Database\\Query\\Processors\\Processor->processInsertGetId(Object(Illuminate\\Database\\Query\\Builder), 'insert into `p_...', Array, NULL)
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Failed\\DatabaseFailedJobProvider.php(63): Illuminate\\Database\\Query\\Builder->insertGetId(Array)
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(280): Illuminate\\Queue\\Failed\\DatabaseFailedJobProvider->log('database', 'default', '{\"uuid\":\"77fad7...', 'Exception: \\xE6\\x96\\x87\\xE6...')
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(186): Illuminate\\Queue\\Console\\WorkCommand->logFailedJob(Object(Illuminate\\Queue\\Events\\JobFailed))
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\events\\Dispatcher.php(458): Illuminate\\Queue\\Console\\WorkCommand->Illuminate\\Queue\\Console\\{closure}(Object(Illuminate\\Queue\\Events\\JobFailed))
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\events\\Dispatcher.php(286): Illuminate\\Events\\Dispatcher->Illuminate\\Events\\{closure}('Illuminate\\\\Queu...', Array)
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\events\\Dispatcher.php(266): Illuminate\\Events\\Dispatcher->invokeListeners('Illuminate\\\\Queu...', Array, false)
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Jobs\\Job.php(218): Illuminate\\Events\\Dispatcher->dispatch('Illuminate\\\\Queu...')
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(596): Illuminate\\Queue\\Jobs\\Job->fail(Object(Exception))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(542): Illuminate\\Queue\\Worker->failJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Exception))
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(466): Illuminate\\Queue\\Worker->markJobAsFailedIfWillExceedMaxAttempts('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 3, Object(Exception))
#15 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(443): Illuminate\\Queue\\Worker->handleJobException('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Queue\\WorkerOptions), Object(Exception))
#16 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(389): Illuminate\\Queue\\Worker->process('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Queue\\WorkerOptions))
#17 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(176): Illuminate\\Queue\\Worker->runJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 'database', Object(Illuminate\\Queue\\WorkerOptions))
#18 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(138): Illuminate\\Queue\\Worker->daemon('database', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#19 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(121): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'default')
#20 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#21 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#23 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Laravel\\Lumen\\Application), Array, Object(Closure))
#24 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Laravel\\Lumen\\Application), Array, Array, NULL)
#25 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#26 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\console\\Command.php(181): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#28 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(1078): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Console\\Kernel.php(160): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\artisan(35): Laravel\\Lumen\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 {main}

[previous exception] [object] (PDOException(code: HY000): SQLSTATE[HY000]: General error: 1364 Field 'uuid' doesn't have a default value at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Connection.php:580)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Connection.php(580): PDOStatement->execute()
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('insert into `p_...', Array)
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('insert into `p_...', Array, Object(Closure))
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Connection.php(581): Illuminate\\Database\\Connection->run('insert into `p_...', Array, Object(Closure))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Connection.php(533): Illuminate\\Database\\Connection->statement('insert into `p_...', Array)
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Query\\Processors\\Processor.php(32): Illuminate\\Database\\Connection->insert('insert into `p_...', Array)
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Query\\Builder.php(3387): Illuminate\\Database\\Query\\Processors\\Processor->processInsertGetId(Object(Illuminate\\Database\\Query\\Builder), 'insert into `p_...', Array, NULL)
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Failed\\DatabaseFailedJobProvider.php(63): Illuminate\\Database\\Query\\Builder->insertGetId(Array)
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(280): Illuminate\\Queue\\Failed\\DatabaseFailedJobProvider->log('database', 'default', '{\"uuid\":\"77fad7...', 'Exception: \\xE6\\x96\\x87\\xE6...')
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(186): Illuminate\\Queue\\Console\\WorkCommand->logFailedJob(Object(Illuminate\\Queue\\Events\\JobFailed))
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\events\\Dispatcher.php(458): Illuminate\\Queue\\Console\\WorkCommand->Illuminate\\Queue\\Console\\{closure}(Object(Illuminate\\Queue\\Events\\JobFailed))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\events\\Dispatcher.php(286): Illuminate\\Events\\Dispatcher->Illuminate\\Events\\{closure}('Illuminate\\\\Queu...', Array)
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\events\\Dispatcher.php(266): Illuminate\\Events\\Dispatcher->invokeListeners('Illuminate\\\\Queu...', Array, false)
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Jobs\\Job.php(218): Illuminate\\Events\\Dispatcher->dispatch('Illuminate\\\\Queu...')
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(596): Illuminate\\Queue\\Jobs\\Job->fail(Object(Exception))
#15 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(542): Illuminate\\Queue\\Worker->failJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Exception))
#16 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(466): Illuminate\\Queue\\Worker->markJobAsFailedIfWillExceedMaxAttempts('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 3, Object(Exception))
#17 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(443): Illuminate\\Queue\\Worker->handleJobException('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Queue\\WorkerOptions), Object(Exception))
#18 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(389): Illuminate\\Queue\\Worker->process('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Queue\\WorkerOptions))
#19 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(176): Illuminate\\Queue\\Worker->runJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 'database', Object(Illuminate\\Queue\\WorkerOptions))
#20 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(138): Illuminate\\Queue\\Worker->daemon('database', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#21 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(121): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'default')
#22 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#23 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#24 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#25 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Laravel\\Lumen\\Application), Array, Object(Closure))
#26 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Laravel\\Lumen\\Application), Array, Array, NULL)
#27 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#28 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#29 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\console\\Command.php(181): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#30 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(1078): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Console\\Kernel.php(160): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#34 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\artisan(35): Laravel\\Lumen\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#35 {main}
"} 
[2025-08-13 16:23:45] production.INFO: WebSocket推送进度 - 任务开始 {"task_id":"text_gen_1754988026_B98EDyFQ","user_id":13,"progress":10,"message":"开始文本生成任务","push_type":"ai_generation_progress","redis_channel":"websocket:push:session_*","push_method":"Redis通道桥接","timestamp":1755073425,"job_class":"App\\Jobs\\ProcessTextGeneration","job_method":"handle"} 
[2025-08-13 16:23:45] production.WARNING: 未找到任务记录 {"task_id":"text_gen_1754988026_B98EDyFQ","user_id":13} 
[2025-08-13 16:23:45] production.INFO: WebSocket推送结果 - 任务开始 {"task_id":"text_gen_1754988026_B98EDyFQ","user_id":13,"push_success":"no","push_method":"Redis通道桥接"} 
[2025-08-13 16:23:45] production.INFO: WebSocket推送进度 - 连接AI平台 {"task_id":"text_gen_1754988026_B98EDyFQ","user_id":13,"progress":30,"message":"连接AI平台","push_type":"ai_generation_progress","redis_channel":"websocket:push:session_*","push_method":"Redis通道桥接","timestamp":1755073425,"job_class":"App\\Jobs\\ProcessTextGeneration","job_method":"handle"} 
[2025-08-13 16:23:45] production.WARNING: 未找到任务记录 {"task_id":"text_gen_1754988026_B98EDyFQ","user_id":13} 
[2025-08-13 16:23:45] production.INFO: WebSocket推送结果 - 连接AI平台 {"task_id":"text_gen_1754988026_B98EDyFQ","user_id":13,"push_success":"no","push_method":"Redis通道桥接"} 
[2025-08-13 16:23:45] production.WARNING: 未找到活跃的WebSocket会话或business_type为空 {"task_id":"text_gen_1754988026_B98EDyFQ","user_id":13} 
[2025-08-13 16:23:45] production.DEBUG: Lumen事务开始 {"context":"AiGenerationService.generateText","level":1,"action":"real_begin","framework":"Lumen 10"} 
[2025-08-13 16:23:45] production.WARNING: 事务回滚 {"context":"AiGenerationService.generateText","reason":"Undefined array key \"estimated_cost\"","original_level":1,"action":"real_rollback"} 
[2025-08-13 16:23:45] production.ERROR: 文本生成任务创建失败 {"method":"App\\Services\\PyApi\\AiGenerationService::generateText","error_context":{"user_id":13,"prompt":"","model_id":null,"project_id":null,"generation_params_count":3},"error":"Undefined array key \"estimated_cost\"","trace":"#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RegistersExceptionHandlers.php(47): Laravel\\Lumen\\Application->handleError(2, 'Undefined array...', 'D:\\\\longtool\\\\php...', 291)
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\AiGenerationService.php(291): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(2, 'Undefined array...', 'D:\\\\longtool\\\\php...', 291)
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Jobs\\ProcessTextGeneration.php(143): App\\Services\\PyApi\\AiGenerationService->generateText(13, '', NULL, NULL, NULL, Array, NULL, 'text_gen_175498...')
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(36): App\\Jobs\\ProcessTextGeneration->handle()
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Laravel\\Lumen\\Application), Array, Object(Closure))
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Laravel\\Lumen\\Application), Array, Array, NULL)
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\bus\\Dispatcher.php(128): Illuminate\\Container\\Container->call(Array)
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(144): Illuminate\\Bus\\Dispatcher->Illuminate\\Bus\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\bus\\Dispatcher.php(132): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\CallQueuedHandler.php(124): Illuminate\\Bus\\Dispatcher->dispatchNow(Object(App\\Jobs\\ProcessTextGeneration), false)
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(144): Illuminate\\Queue\\CallQueuedHandler->Illuminate\\Queue\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#15 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\CallQueuedHandler.php(126): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#16 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\CallQueuedHandler.php(70): Illuminate\\Queue\\CallQueuedHandler->dispatchThroughMiddleware(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(App\\Jobs\\ProcessTextGeneration))
#17 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Jobs\\Job.php(102): Illuminate\\Queue\\CallQueuedHandler->call(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Array)
#18 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(439): Illuminate\\Queue\\Jobs\\Job->fire()
#19 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(389): Illuminate\\Queue\\Worker->process('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Queue\\WorkerOptions))
#20 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(176): Illuminate\\Queue\\Worker->runJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 'database', Object(Illuminate\\Queue\\WorkerOptions))
#21 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(138): Illuminate\\Queue\\Worker->daemon('database', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#22 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(121): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'default')
#23 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#24 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#25 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#26 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Laravel\\Lumen\\Application), Array, Object(Closure))
#27 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Laravel\\Lumen\\Application), Array, Array, NULL)
#28 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#29 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#30 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\console\\Command.php(181): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#31 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(1078): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#34 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Console\\Kernel.php(160): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#35 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\artisan(35): Laravel\\Lumen\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#36 {main}"} 
[2025-08-13 16:23:45] production.INFO: WebSocket推送事件 - 任务失败 {"task_id":"text_gen_1754988026_B98EDyFQ","user_id":13,"error_message":"文本生成任务创建失败","push_type":"ai_generation_failed","redis_channel":"websocket:push:session_*","push_method":"Redis通道桥接","timestamp":1755073425,"job_class":"App\\Jobs\\ProcessTextGeneration","job_method":"handle"} 
[2025-08-13 16:23:45] production.WARNING: 未找到任务记录 {"task_id":"text_gen_1754988026_B98EDyFQ","user_id":13} 
[2025-08-13 16:23:45] production.INFO: WebSocket推送结果 - 任务失败 {"task_id":"text_gen_1754988026_B98EDyFQ","user_id":13,"push_success":"no","push_method":"Redis通道桥接","error_message":"文本生成任务创建失败"} 
[2025-08-13 16:23:45] production.ERROR: 发布文本生成失败事件异常 {"task_id":"text_gen_1754988026_B98EDyFQ","error":"cURL error 60: SSL certificate problem: unable to get local issuer certificate (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.tiptop.cn/py-api/events/publish"} 
[2025-08-13 16:23:45] production.ERROR: 文本生成任务失败 {"task_id":"text_gen_1754988026_B98EDyFQ","user_id":13,"context":"prompt_edit","prompt":"请写一首关于春天的诗","error":"文本生成任务创建失败","trace":"#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(36): App\\Jobs\\ProcessTextGeneration->handle()
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Laravel\\Lumen\\Application), Array, Object(Closure))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Laravel\\Lumen\\Application), Array, Array, NULL)
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\bus\\Dispatcher.php(128): Illuminate\\Container\\Container->call(Array)
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(144): Illuminate\\Bus\\Dispatcher->Illuminate\\Bus\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\bus\\Dispatcher.php(132): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\CallQueuedHandler.php(124): Illuminate\\Bus\\Dispatcher->dispatchNow(Object(App\\Jobs\\ProcessTextGeneration), false)
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(144): Illuminate\\Queue\\CallQueuedHandler->Illuminate\\Queue\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\CallQueuedHandler.php(126): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\CallQueuedHandler.php(70): Illuminate\\Queue\\CallQueuedHandler->dispatchThroughMiddleware(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(App\\Jobs\\ProcessTextGeneration))
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Jobs\\Job.php(102): Illuminate\\Queue\\CallQueuedHandler->call(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Array)
#15 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(439): Illuminate\\Queue\\Jobs\\Job->fire()
#16 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(389): Illuminate\\Queue\\Worker->process('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Queue\\WorkerOptions))
#17 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(176): Illuminate\\Queue\\Worker->runJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 'database', Object(Illuminate\\Queue\\WorkerOptions))
#18 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(138): Illuminate\\Queue\\Worker->daemon('database', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#19 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(121): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'default')
#20 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#21 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#23 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Laravel\\Lumen\\Application), Array, Object(Closure))
#24 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Laravel\\Lumen\\Application), Array, Array, NULL)
#25 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#26 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\console\\Command.php(181): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#28 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(1078): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Console\\Kernel.php(160): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\artisan(35): Laravel\\Lumen\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 {main}"} 
[2025-08-13 16:23:45] production.INFO: WebSocket推送事件 - 任务最终失败 {"task_id":"text_gen_1754988026_B98EDyFQ","user_id":13,"error_message":"文本生成任务创建失败","push_type":"ai_generation_failed","context":"failed_method"} 
[2025-08-13 16:23:45] production.WARNING: 未找到任务记录 {"task_id":"text_gen_1754988026_B98EDyFQ","user_id":13} 
[2025-08-13 16:23:45] production.ERROR: 发布文本生成失败事件异常 {"task_id":"text_gen_1754988026_B98EDyFQ","error":"cURL error 60: SSL certificate problem: unable to get local issuer certificate (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.tiptop.cn/py-api/events/publish"} 
[2025-08-13 16:23:45] production.ERROR: 文本生成任务最终失败 {"task_id":"text_gen_1754988026_B98EDyFQ","user_id":13,"context":"prompt_edit","error":"文本生成任务创建失败"} 
[2025-08-13 16:23:45] production.ERROR: SQLSTATE[HY000]: General error: 1364 Field 'uuid' doesn't have a default value (Connection: mysql, SQL: insert into `p_failed_jobs` (`connection`, `queue`, `payload`, `exception`, `failed_at`) values (database, default, {"uuid":"f05e16da-d4ca-46fd-b0f9-e5f906e7b661","displayName":"App\\Jobs\\ProcessTextGeneration","job":"Illuminate\\Queue\\CallQueuedHandler@call","maxTries":3,"maxExceptions":null,"failOnTimeout":false,"backoff":null,"timeout":180,"retryUntil":null,"data":{"commandName":"App\\Jobs\\ProcessTextGeneration","command":"O:30:\"App\\Jobs\\ProcessTextGeneration\":5:{s:9:\"\u0000*\u0000taskId\";s:28:\"text_gen_1754988026_B98EDyFQ\";s:9:\"\u0000*\u0000userId\";i:13;s:9:\"\u0000*\u0000prompt\";s:30:\"请写一首关于春天的诗\";s:19:\"\u0000*\u0000generationParams\";a:3:{s:10:\"max_tokens\";i:1000;s:11:\"temperature\";d:0.7;s:5:\"top_p\";d:0.9;}s:10:\"\u0000*\u0000context\";s:11:\"prompt_edit\";}"}}, Exception: 文本生成任务创建失败 in D:\longtool\phpStudy_64\WWW\tool_api\php\api\app\Jobs\ProcessTextGeneration.php:226
Stack trace:
#0 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\container\BoundMethod.php(36): App\Jobs\ProcessTextGeneration->handle()
#1 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\container\Util.php(41): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#2 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\container\BoundMethod.php(93): Illuminate\Container\Util::unwrapIfClosure(Object(Closure))
#3 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\container\BoundMethod.php(37): Illuminate\Container\BoundMethod::callBoundMethod(Object(Laravel\Lumen\Application), Array, Object(Closure))
#4 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\container\Container.php(662): Illuminate\Container\BoundMethod::call(Object(Laravel\Lumen\Application), Array, Array, NULL)
#5 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\bus\Dispatcher.php(128): Illuminate\Container\Container->call(Array)
#6 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\pipeline\Pipeline.php(144): Illuminate\Bus\Dispatcher->Illuminate\Bus\{closure}(Object(App\Jobs\ProcessTextGeneration))
#7 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\pipeline\Pipeline.php(119): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(App\Jobs\ProcessTextGeneration))
#8 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\bus\Dispatcher.php(132): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#9 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\queue\CallQueuedHandler.php(124): Illuminate\Bus\Dispatcher->dispatchNow(Object(App\Jobs\ProcessTextGeneration), false)
#10 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\pipeline\Pipeline.php(144): Illuminate\Queue\CallQueuedHandler->Illuminate\Queue\{closure}(Object(App\Jobs\ProcessTextGeneration))
#11 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\pipeline\Pipeline.php(119): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(App\Jobs\ProcessTextGeneration))
#12 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\queue\CallQueuedHandler.php(126): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#13 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\queue\CallQueuedHandler.php(70): Illuminate\Queue\CallQueuedHandler->dispatchThroughMiddleware(Object(Illuminate\Queue\Jobs\DatabaseJob), Object(App\Jobs\ProcessTextGeneration))
#14 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\queue\Jobs\Job.php(102): Illuminate\Queue\CallQueuedHandler->call(Object(Illuminate\Queue\Jobs\DatabaseJob), Array)
#15 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\queue\Worker.php(439): Illuminate\Queue\Jobs\Job->fire()
#16 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\queue\Worker.php(389): Illuminate\Queue\Worker->process('database', Object(Illuminate\Queue\Jobs\DatabaseJob), Object(Illuminate\Queue\WorkerOptions))
#17 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\queue\Worker.php(176): Illuminate\Queue\Worker->runJob(Object(Illuminate\Queue\Jobs\DatabaseJob), 'database', Object(Illuminate\Queue\WorkerOptions))
#18 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\queue\Console\WorkCommand.php(138): Illuminate\Queue\Worker->daemon('database', 'default', Object(Illuminate\Queue\WorkerOptions))
#19 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\queue\Console\WorkCommand.php(121): Illuminate\Queue\Console\WorkCommand->runWorker('database', 'default')
#20 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\container\BoundMethod.php(36): Illuminate\Queue\Console\WorkCommand->handle()
#21 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\container\Util.php(41): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#22 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\container\BoundMethod.php(93): Illuminate\Container\Util::unwrapIfClosure(Object(Closure))
#23 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\container\BoundMethod.php(37): Illuminate\Container\BoundMethod::callBoundMethod(Object(Laravel\Lumen\Application), Array, Object(Closure))
#24 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\container\Container.php(662): Illuminate\Container\BoundMethod::call(Object(Laravel\Lumen\Application), Array, Array, NULL)
#25 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\console\Command.php(211): Illuminate\Container\Container->call(Array)
#26 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\symfony\console\Command\Command.php(326): Illuminate\Console\Command->execute(Object(Symfony\Component\Console\Input\ArgvInput), Object(Illuminate\Console\OutputStyle))
#27 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\console\Command.php(181): Symfony\Component\Console\Command\Command->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Illuminate\Console\OutputStyle))
#28 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\symfony\console\Application.php(1078): Illuminate\Console\Command->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#29 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\symfony\console\Application.php(324): Symfony\Component\Console\Application->doRunCommand(Object(Illuminate\Queue\Console\WorkCommand), Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#30 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\symfony\console\Application.php(175): Symfony\Component\Console\Application->doRun(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#31 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\laravel\lumen-framework\src\Console\Kernel.php(160): Symfony\Component\Console\Application->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#32 D:\longtool\phpStudy_64\WWW\tool_api\php\api\artisan(35): Laravel\Lumen\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#33 {main}, 2025-08-13 16:23:45)) {"exception":"[object] (Illuminate\\Database\\QueryException(code: HY000): SQLSTATE[HY000]: General error: 1364 Field 'uuid' doesn't have a default value (Connection: mysql, SQL: insert into `p_failed_jobs` (`connection`, `queue`, `payload`, `exception`, `failed_at`) values (database, default, {\"uuid\":\"f05e16da-d4ca-46fd-b0f9-e5f906e7b661\",\"displayName\":\"App\\\\Jobs\\\\ProcessTextGeneration\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":3,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":180,\"retryUntil\":null,\"data\":{\"commandName\":\"App\\\\Jobs\\\\ProcessTextGeneration\",\"command\":\"O:30:\\\"App\\\\Jobs\\\\ProcessTextGeneration\\\":5:{s:9:\\\"\\u0000*\\u0000taskId\\\";s:28:\\\"text_gen_1754988026_B98EDyFQ\\\";s:9:\\\"\\u0000*\\u0000userId\\\";i:13;s:9:\\\"\\u0000*\\u0000prompt\\\";s:30:\\\"请写一首关于春天的诗\\\";s:19:\\\"\\u0000*\\u0000generationParams\\\";a:3:{s:10:\\\"max_tokens\\\";i:1000;s:11:\\\"temperature\\\";d:0.7;s:5:\\\"top_p\\\";d:0.9;}s:10:\\\"\\u0000*\\u0000context\\\";s:11:\\\"prompt_edit\\\";}\"}}, Exception: 文本生成任务创建失败 in D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Jobs\\ProcessTextGeneration.php:226
Stack trace:
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(36): App\\Jobs\\ProcessTextGeneration->handle()
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Laravel\\Lumen\\Application), Array, Object(Closure))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Laravel\\Lumen\\Application), Array, Array, NULL)
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\bus\\Dispatcher.php(128): Illuminate\\Container\\Container->call(Array)
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(144): Illuminate\\Bus\\Dispatcher->Illuminate\\Bus\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\bus\\Dispatcher.php(132): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\CallQueuedHandler.php(124): Illuminate\\Bus\\Dispatcher->dispatchNow(Object(App\\Jobs\\ProcessTextGeneration), false)
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(144): Illuminate\\Queue\\CallQueuedHandler->Illuminate\\Queue\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\CallQueuedHandler.php(126): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\CallQueuedHandler.php(70): Illuminate\\Queue\\CallQueuedHandler->dispatchThroughMiddleware(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(App\\Jobs\\ProcessTextGeneration))
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Jobs\\Job.php(102): Illuminate\\Queue\\CallQueuedHandler->call(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Array)
#15 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(439): Illuminate\\Queue\\Jobs\\Job->fire()
#16 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(389): Illuminate\\Queue\\Worker->process('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Queue\\WorkerOptions))
#17 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(176): Illuminate\\Queue\\Worker->runJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 'database', Object(Illuminate\\Queue\\WorkerOptions))
#18 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(138): Illuminate\\Queue\\Worker->daemon('database', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#19 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(121): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'default')
#20 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#21 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#23 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Laravel\\Lumen\\Application), Array, Object(Closure))
#24 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Laravel\\Lumen\\Application), Array, Array, NULL)
#25 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#26 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\console\\Command.php(181): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#28 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(1078): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Console\\Kernel.php(160): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\artisan(35): Laravel\\Lumen\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 {main}, 2025-08-13 16:23:45)) at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Connection.php:822)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('insert into `p_...', Array, Object(Closure))
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Connection.php(581): Illuminate\\Database\\Connection->run('insert into `p_...', Array, Object(Closure))
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Connection.php(533): Illuminate\\Database\\Connection->statement('insert into `p_...', Array)
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Query\\Processors\\Processor.php(32): Illuminate\\Database\\Connection->insert('insert into `p_...', Array)
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Query\\Builder.php(3387): Illuminate\\Database\\Query\\Processors\\Processor->processInsertGetId(Object(Illuminate\\Database\\Query\\Builder), 'insert into `p_...', Array, NULL)
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Failed\\DatabaseFailedJobProvider.php(63): Illuminate\\Database\\Query\\Builder->insertGetId(Array)
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(280): Illuminate\\Queue\\Failed\\DatabaseFailedJobProvider->log('database', 'default', '{\"uuid\":\"f05e16...', 'Exception: \\xE6\\x96\\x87\\xE6...')
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(186): Illuminate\\Queue\\Console\\WorkCommand->logFailedJob(Object(Illuminate\\Queue\\Events\\JobFailed))
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\events\\Dispatcher.php(458): Illuminate\\Queue\\Console\\WorkCommand->Illuminate\\Queue\\Console\\{closure}(Object(Illuminate\\Queue\\Events\\JobFailed))
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\events\\Dispatcher.php(286): Illuminate\\Events\\Dispatcher->Illuminate\\Events\\{closure}('Illuminate\\\\Queu...', Array)
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\events\\Dispatcher.php(266): Illuminate\\Events\\Dispatcher->invokeListeners('Illuminate\\\\Queu...', Array, false)
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Jobs\\Job.php(218): Illuminate\\Events\\Dispatcher->dispatch('Illuminate\\\\Queu...')
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(596): Illuminate\\Queue\\Jobs\\Job->fail(Object(Exception))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(542): Illuminate\\Queue\\Worker->failJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Exception))
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(466): Illuminate\\Queue\\Worker->markJobAsFailedIfWillExceedMaxAttempts('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 3, Object(Exception))
#15 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(443): Illuminate\\Queue\\Worker->handleJobException('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Queue\\WorkerOptions), Object(Exception))
#16 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(389): Illuminate\\Queue\\Worker->process('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Queue\\WorkerOptions))
#17 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(176): Illuminate\\Queue\\Worker->runJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 'database', Object(Illuminate\\Queue\\WorkerOptions))
#18 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(138): Illuminate\\Queue\\Worker->daemon('database', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#19 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(121): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'default')
#20 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#21 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#23 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Laravel\\Lumen\\Application), Array, Object(Closure))
#24 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Laravel\\Lumen\\Application), Array, Array, NULL)
#25 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#26 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\console\\Command.php(181): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#28 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(1078): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Console\\Kernel.php(160): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\artisan(35): Laravel\\Lumen\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 {main}

[previous exception] [object] (PDOException(code: HY000): SQLSTATE[HY000]: General error: 1364 Field 'uuid' doesn't have a default value at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Connection.php:580)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Connection.php(580): PDOStatement->execute()
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('insert into `p_...', Array)
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('insert into `p_...', Array, Object(Closure))
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Connection.php(581): Illuminate\\Database\\Connection->run('insert into `p_...', Array, Object(Closure))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Connection.php(533): Illuminate\\Database\\Connection->statement('insert into `p_...', Array)
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Query\\Processors\\Processor.php(32): Illuminate\\Database\\Connection->insert('insert into `p_...', Array)
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Query\\Builder.php(3387): Illuminate\\Database\\Query\\Processors\\Processor->processInsertGetId(Object(Illuminate\\Database\\Query\\Builder), 'insert into `p_...', Array, NULL)
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Failed\\DatabaseFailedJobProvider.php(63): Illuminate\\Database\\Query\\Builder->insertGetId(Array)
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(280): Illuminate\\Queue\\Failed\\DatabaseFailedJobProvider->log('database', 'default', '{\"uuid\":\"f05e16...', 'Exception: \\xE6\\x96\\x87\\xE6...')
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(186): Illuminate\\Queue\\Console\\WorkCommand->logFailedJob(Object(Illuminate\\Queue\\Events\\JobFailed))
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\events\\Dispatcher.php(458): Illuminate\\Queue\\Console\\WorkCommand->Illuminate\\Queue\\Console\\{closure}(Object(Illuminate\\Queue\\Events\\JobFailed))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\events\\Dispatcher.php(286): Illuminate\\Events\\Dispatcher->Illuminate\\Events\\{closure}('Illuminate\\\\Queu...', Array)
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\events\\Dispatcher.php(266): Illuminate\\Events\\Dispatcher->invokeListeners('Illuminate\\\\Queu...', Array, false)
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Jobs\\Job.php(218): Illuminate\\Events\\Dispatcher->dispatch('Illuminate\\\\Queu...')
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(596): Illuminate\\Queue\\Jobs\\Job->fail(Object(Exception))
#15 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(542): Illuminate\\Queue\\Worker->failJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Exception))
#16 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(466): Illuminate\\Queue\\Worker->markJobAsFailedIfWillExceedMaxAttempts('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 3, Object(Exception))
#17 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(443): Illuminate\\Queue\\Worker->handleJobException('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Queue\\WorkerOptions), Object(Exception))
#18 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(389): Illuminate\\Queue\\Worker->process('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Queue\\WorkerOptions))
#19 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(176): Illuminate\\Queue\\Worker->runJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 'database', Object(Illuminate\\Queue\\WorkerOptions))
#20 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(138): Illuminate\\Queue\\Worker->daemon('database', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#21 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(121): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'default')
#22 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#23 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#24 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#25 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Laravel\\Lumen\\Application), Array, Object(Closure))
#26 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Laravel\\Lumen\\Application), Array, Array, NULL)
#27 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#28 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#29 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\console\\Command.php(181): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#30 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(1078): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Console\\Kernel.php(160): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#34 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\artisan(35): Laravel\\Lumen\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#35 {main}
"} 
[2025-08-13 16:26:44] production.INFO: 积分检查 {"user_id":14,"required_amount":0.5842,"current_balance":"93.23","sufficient":true,"business_type":"text_generation","business_id":null} 
[2025-08-13 16:26:44] production.INFO: 积分冻结成功 {"user_id":14,"amount":0.5842,"business_type":"text_generation","business_id":null,"transaction_id":31,"freeze_id":31} 
[2025-08-13 16:27:09] production.INFO: WebSocket推送进度 - 任务开始 {"task_id":"text_gen_1755073604_N3L7twtm","user_id":14,"progress":10,"message":"开始文本生成任务","push_type":"ai_generation_progress","redis_channel":"websocket:push:session_*","push_method":"Redis通道桥接","timestamp":1755073629,"job_class":"App\\Jobs\\ProcessTextGeneration","job_method":"handle"} 
[2025-08-13 16:27:09] production.WARNING: 未找到任务记录 {"task_id":"text_gen_1755073604_N3L7twtm","user_id":14} 
[2025-08-13 16:27:09] production.INFO: WebSocket推送结果 - 任务开始 {"task_id":"text_gen_1755073604_N3L7twtm","user_id":14,"push_success":"no","push_method":"Redis通道桥接"} 
[2025-08-13 16:27:09] production.INFO: WebSocket推送进度 - 连接AI平台 {"task_id":"text_gen_1755073604_N3L7twtm","user_id":14,"progress":30,"message":"连接AI平台","push_type":"ai_generation_progress","redis_channel":"websocket:push:session_*","push_method":"Redis通道桥接","timestamp":1755073629,"job_class":"App\\Jobs\\ProcessTextGeneration","job_method":"handle"} 
[2025-08-13 16:27:09] production.WARNING: 未找到任务记录 {"task_id":"text_gen_1755073604_N3L7twtm","user_id":14} 
[2025-08-13 16:27:09] production.INFO: WebSocket推送结果 - 连接AI平台 {"task_id":"text_gen_1755073604_N3L7twtm","user_id":14,"push_success":"no","push_method":"Redis通道桥接"} 
[2025-08-13 16:27:09] production.INFO: 从WebSocket会话获取business_type {"task_id":"text_gen_1755073604_N3L7twtm","user_id":14,"session_id":"ws_XeLhDmQCfkQrn6vNY7DA91L0UF3erivH","business_type":"text_generation_storyboard"} 
[2025-08-13 16:27:09] production.DEBUG: Lumen事务开始 {"context":"AiGenerationService.generateText","level":1,"action":"real_begin","framework":"Lumen 10"} 
[2025-08-13 16:27:09] production.INFO: AI服务调用 {"platform":"deepseek","mode":"mock","data_size":118} 
[2025-08-13 16:27:09] production.INFO: 调用模拟服务 {"platform":"deepseek","url":"https://aiapi.tiptop.cn/deepseek/chat/completions","timeout":"30"} 
[2025-08-13 16:27:09] production.INFO: 模拟服务调用成功 {"platform":"deepseek","status":200,"response_size":0} 
[2025-08-13 16:27:09] production.INFO: 用户偏好记录更新成功 {"user_id":14,"platform":"deepseek","task_type":"text_generation_storyboard","usage_count":1,"success_rate":1.0} 
[2025-08-13 16:27:09] production.INFO: 积分消费成功 {"transaction_id":31,"user_id":14,"amount":"0.58"} 
[2025-08-13 16:27:09] production.ERROR: AI文本生成失败，跳过后续处理 {"user_id":14,"task_id":31,"ai_response_code":200,"ai_response_message":"文本生成成功","has_text_content":false} 
[2025-08-13 16:27:09] production.WARNING: 事务回滚 {"context":"AiGenerationService.generateText","reason":"AI文本生成失败","original_level":1,"action":"real_rollback"} 
[2025-08-13 16:27:09] production.INFO: WebSocket推送进度 - 保存文本数据 {"task_id":"text_gen_1755073604_N3L7twtm","user_id":14,"progress":80,"message":"保存文本数据","push_type":"ai_generation_progress","redis_channel":"websocket:push:session_*","push_method":"Redis通道桥接","timestamp":1755073629,"job_class":"App\\Jobs\\ProcessTextGeneration","job_method":"handle"} 
[2025-08-13 16:27:09] production.WARNING: 未找到任务记录 {"task_id":"text_gen_1755073604_N3L7twtm","user_id":14} 
[2025-08-13 16:27:09] production.INFO: WebSocket推送结果 - 保存文本数据 {"task_id":"text_gen_1755073604_N3L7twtm","user_id":14,"push_success":"no","push_method":"Redis通道桥接"} 
[2025-08-13 16:27:09] production.INFO: WebSocket推送进度 - 文本生成完成 {"task_id":"text_gen_1755073604_N3L7twtm","user_id":14,"progress":100,"message":"文本生成完成","push_type":"ai_generation_progress"} 
[2025-08-13 16:27:09] production.WARNING: 未找到任务记录 {"task_id":"text_gen_1755073604_N3L7twtm","user_id":14} 
[2025-08-13 16:27:09] production.INFO: WebSocket推送事件 - 任务完成 {"task_id":"text_gen_1755073604_N3L7twtm","user_id":14,"push_type":"ai_generation_completed","redis_channel":"websocket:push:session_*","push_method":"Redis通道桥接","timestamp":1755073629,"job_class":"App\\Jobs\\ProcessTextGeneration","job_method":"handle"} 
[2025-08-13 16:27:09] production.WARNING: 未找到任务记录 {"task_id":"text_gen_1755073604_N3L7twtm","user_id":14} 
[2025-08-13 16:27:09] production.INFO: WebSocket推送结果 - 任务完成 {"task_id":"text_gen_1755073604_N3L7twtm","user_id":14,"push_success":"no","push_method":"Redis通道桥接"} 
[2025-08-13 16:27:09] production.INFO: 文本生成任务完成 {"task_id":"text_gen_1755073604_N3L7twtm","user_id":14,"context":"text","ai_task_id":31,"cost":0} 
[2025-08-13 16:29:39] production.ERROR: SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'p_character_library' already exists (Connection: mysql, SQL: create table `p_character_library` (`id` bigint unsigned not null auto_increment primary key comment '角色ID', `name` varchar(100) not null comment '角色名称', `description` text null comment '角色描述', `avatar_url` varchar(500) null comment '角色头像URL', `category` varchar(50) null comment '角色分类', `tags` json null comment '角色标签', `style` varchar(100) null comment '角色风格', `gender` enum('male', 'female', 'other') null comment '性别', `age_range` varchar(20) null comment '年龄范围', `personality` text null comment '性格特征', `appearance` text null comment '外观描述', `background_story` text null comment '背景故事', `generation_params` json null comment '生成参数', `created_by` bigint unsigned null comment '创建者ID', `project_id` bigint unsigned null comment '关联项目ID', `source_file_id` varchar(100) null comment '源文件ID', `is_active` tinyint(1) not null default '1' comment '是否激活', `is_featured` tinyint(1) not null default '0' comment '是否推荐', `is_public` tinyint(1) not null default '0' comment '是否公开', `usage_count` int not null default '0' comment '使用次数', `rating` decimal(3, 2) not null default '0' comment '评分', `metadata` json null comment '元数据', `created_at` timestamp null, `updated_at` timestamp null) default character set utf8mb4 collate 'utf8mb4_unicode_ci' engine = InnoDB) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S01): SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'p_character_library' already exists (Connection: mysql, SQL: create table `p_character_library` (`id` bigint unsigned not null auto_increment primary key comment '角色ID', `name` varchar(100) not null comment '角色名称', `description` text null comment '角色描述', `avatar_url` varchar(500) null comment '角色头像URL', `category` varchar(50) null comment '角色分类', `tags` json null comment '角色标签', `style` varchar(100) null comment '角色风格', `gender` enum('male', 'female', 'other') null comment '性别', `age_range` varchar(20) null comment '年龄范围', `personality` text null comment '性格特征', `appearance` text null comment '外观描述', `background_story` text null comment '背景故事', `generation_params` json null comment '生成参数', `created_by` bigint unsigned null comment '创建者ID', `project_id` bigint unsigned null comment '关联项目ID', `source_file_id` varchar(100) null comment '源文件ID', `is_active` tinyint(1) not null default '1' comment '是否激活', `is_featured` tinyint(1) not null default '0' comment '是否推荐', `is_public` tinyint(1) not null default '0' comment '是否公开', `usage_count` int not null default '0' comment '使用次数', `rating` decimal(3, 2) not null default '0' comment '评分', `metadata` json null comment '元数据', `created_at` timestamp null, `updated_at` timestamp null) default character set utf8mb4 collate 'utf8mb4_unicode_ci' engine = InnoDB) at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Connection.php:822)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('create table `p...', Array, Object(Closure))
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Connection.php(581): Illuminate\\Database\\Connection->run('create table `p...', Array, Object(Closure))
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Schema\\Blueprint.php(110): Illuminate\\Database\\Connection->statement('create table `p...')
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Schema\\Builder.php(552): Illuminate\\Database\\Schema\\Blueprint->build(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Schema\\Grammars\\MySqlGrammar))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Schema\\Builder.php(410): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\support\\Facades\\Facade.php(355): Illuminate\\Database\\Schema\\Builder->create('character_libra...', Object(Closure))
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\database\\migrations\\2024_01_01_000001_create_character_library_table.php(65): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Migrations\\Migrator.php(493): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Migrations\\Migrator.php(410): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Migrations\\Migrator.php(419): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Migrations\\Migrator.php(216): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\console\\View\\Components\\Task.php(37): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Migrations\\Migrator.php(756): Illuminate\\Console\\View\\Components\\Task->render('2024_01_01_0000...', Object(Closure))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Migrations\\Migrator.php(216): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2024_01_01_0000...', Object(Closure))
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Migrations\\Migrator.php(181): Illuminate\\Database\\Migrations\\Migrator->runUp('D:\\\\longtool\\\\php...', 10, false)
#15 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Migrations\\Migrator.php(124): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#16 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Console\\Migrations\\MigrateCommand.php(92): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#17 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Migrations\\Migrator.php(633): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#18 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Console\\Migrations\\MigrateCommand.php(104): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#19 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#20 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#21 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#22 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Laravel\\Lumen\\Application), Array, Object(Closure))
#23 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Laravel\\Lumen\\Application), Array, Array, NULL)
#24 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#25 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#26 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\console\\Command.php(181): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(1078): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Console\\Kernel.php(160): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\artisan(35): Laravel\\Lumen\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 {main}

[previous exception] [object] (PDOException(code: 42S01): SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'p_character_library' already exists at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Connection.php:580)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Connection.php(580): PDOStatement->execute()
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('create table `p...', Array)
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('create table `p...', Array, Object(Closure))
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Connection.php(581): Illuminate\\Database\\Connection->run('create table `p...', Array, Object(Closure))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Schema\\Blueprint.php(110): Illuminate\\Database\\Connection->statement('create table `p...')
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Schema\\Builder.php(552): Illuminate\\Database\\Schema\\Blueprint->build(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Schema\\Grammars\\MySqlGrammar))
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Schema\\Builder.php(410): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\support\\Facades\\Facade.php(355): Illuminate\\Database\\Schema\\Builder->create('character_libra...', Object(Closure))
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\database\\migrations\\2024_01_01_000001_create_character_library_table.php(65): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Migrations\\Migrator.php(493): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Migrations\\Migrator.php(410): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Migrations\\Migrator.php(419): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Migrations\\Migrator.php(216): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\console\\View\\Components\\Task.php(37): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Migrations\\Migrator.php(756): Illuminate\\Console\\View\\Components\\Task->render('2024_01_01_0000...', Object(Closure))
#15 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Migrations\\Migrator.php(216): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2024_01_01_0000...', Object(Closure))
#16 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Migrations\\Migrator.php(181): Illuminate\\Database\\Migrations\\Migrator->runUp('D:\\\\longtool\\\\php...', 10, false)
#17 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Migrations\\Migrator.php(124): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#18 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Console\\Migrations\\MigrateCommand.php(92): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#19 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Migrations\\Migrator.php(633): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#20 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Console\\Migrations\\MigrateCommand.php(104): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#21 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#22 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#23 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#24 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Laravel\\Lumen\\Application), Array, Object(Closure))
#25 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Laravel\\Lumen\\Application), Array, Array, NULL)
#26 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#27 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#28 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\console\\Command.php(181): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#29 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(1078): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Console\\Kernel.php(160): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\artisan(35): Laravel\\Lumen\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#34 {main}
"} 
[2025-08-13 16:41:52] production.ERROR: Command "tinker" is not defined. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\CommandNotFoundException(code: 0): Command \"tinker\" is not defined. at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php:737)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(266): Symfony\\Component\\Console\\Application->find('tinker')
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Console\\Kernel.php(160): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\artisan(35): Laravel\\Lumen\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 {main}
"} 
[2025-08-13 16:48:25] production.ERROR: Redis通道订阅失败 {"error":"read error on connection to 127.0.0.1:6379"} 
[2025-08-13 16:48:56] production.INFO: 积分检查 {"user_id":14,"required_amount":0.5894,"current_balance":"92.65","sufficient":true,"business_type":"text_generation","business_id":null} 
[2025-08-13 16:48:56] production.INFO: 积分冻结成功 {"user_id":14,"amount":0.5894,"business_type":"text_generation","business_id":null,"transaction_id":32,"freeze_id":32} 
[2025-08-13 16:48:58] production.INFO: WebSocket推送进度 - 任务开始 {"task_id":"text_gen_1755074936_NspwGeGP","user_id":14,"progress":10,"message":"开始文本生成任务","push_type":"ai_generation_progress","redis_channel":"websocket:push:session_*","push_method":"Redis通道桥接","timestamp":1755074938,"job_class":"App\\Jobs\\ProcessTextGeneration","job_method":"handle"} 
[2025-08-13 16:48:58] production.WARNING: 未找到任务记录 {"task_id":"text_gen_1755074936_NspwGeGP","user_id":14} 
[2025-08-13 16:48:58] production.INFO: WebSocket推送结果 - 任务开始 {"task_id":"text_gen_1755074936_NspwGeGP","user_id":14,"push_success":"no","push_method":"Redis通道桥接"} 
[2025-08-13 16:48:58] production.INFO: WebSocket推送进度 - 连接AI平台 {"task_id":"text_gen_1755074936_NspwGeGP","user_id":14,"progress":30,"message":"连接AI平台","push_type":"ai_generation_progress","redis_channel":"websocket:push:session_*","push_method":"Redis通道桥接","timestamp":1755074938,"job_class":"App\\Jobs\\ProcessTextGeneration","job_method":"handle"} 
[2025-08-13 16:48:58] production.WARNING: 未找到任务记录 {"task_id":"text_gen_1755074936_NspwGeGP","user_id":14} 
[2025-08-13 16:48:58] production.INFO: WebSocket推送结果 - 连接AI平台 {"task_id":"text_gen_1755074936_NspwGeGP","user_id":14,"push_success":"no","push_method":"Redis通道桥接"} 
[2025-08-13 16:48:58] production.INFO: 从WebSocket会话获取business_type {"task_id":"text_gen_1755074936_NspwGeGP","user_id":14,"session_id":"ws_XeLhDmQCfkQrn6vNY7DA91L0UF3erivH","business_type":"text_generation_storyboard"} 
[2025-08-13 16:48:58] production.DEBUG: Lumen事务开始 {"context":"AiGenerationService.generateText","level":1,"action":"real_begin","framework":"Lumen 10"} 
[2025-08-13 16:48:58] production.INFO: AI服务调用 {"platform":"deepseek","mode":"mock","data_size":118} 
[2025-08-13 16:48:58] production.INFO: 调用模拟服务 {"platform":"deepseek","url":"https://aiapi.tiptop.cn/deepseek/chat/completions","timeout":"30"} 
[2025-08-13 16:48:58] production.INFO: 模拟服务调用成功 {"platform":"deepseek","status":200,"response_size":0} 
[2025-08-13 16:48:58] production.INFO: 用户偏好记录更新成功 {"user_id":14,"platform":"deepseek","task_type":"text_generation_storyboard","usage_count":1,"success_rate":1.0} 
[2025-08-13 16:48:58] production.INFO: 积分消费成功 {"transaction_id":32,"user_id":14,"amount":"0.59"} 
[2025-08-13 16:48:58] production.ERROR: AI文本生成失败，跳过后续处理 {"user_id":14,"task_id":32,"ai_response_code":200,"ai_response_message":"文本生成成功","has_text_content":false} 
[2025-08-13 16:48:58] production.WARNING: 事务回滚 {"context":"AiGenerationService.generateText","reason":"AI文本生成失败","original_level":1,"action":"real_rollback"} 
[2025-08-13 16:48:58] production.INFO: WebSocket推送进度 - 保存文本数据 {"task_id":"text_gen_1755074936_NspwGeGP","user_id":14,"progress":80,"message":"保存文本数据","push_type":"ai_generation_progress","redis_channel":"websocket:push:session_*","push_method":"Redis通道桥接","timestamp":1755074938,"job_class":"App\\Jobs\\ProcessTextGeneration","job_method":"handle"} 
[2025-08-13 16:48:58] production.WARNING: 未找到任务记录 {"task_id":"text_gen_1755074936_NspwGeGP","user_id":14} 
[2025-08-13 16:48:58] production.INFO: WebSocket推送结果 - 保存文本数据 {"task_id":"text_gen_1755074936_NspwGeGP","user_id":14,"push_success":"no","push_method":"Redis通道桥接"} 
[2025-08-13 16:48:58] production.INFO: WebSocket推送进度 - 文本生成完成 {"task_id":"text_gen_1755074936_NspwGeGP","user_id":14,"progress":100,"message":"文本生成完成","push_type":"ai_generation_progress"} 
[2025-08-13 16:48:58] production.WARNING: 未找到任务记录 {"task_id":"text_gen_1755074936_NspwGeGP","user_id":14} 
[2025-08-13 16:48:58] production.INFO: WebSocket推送事件 - 任务完成 {"task_id":"text_gen_1755074936_NspwGeGP","user_id":14,"push_type":"ai_generation_completed","redis_channel":"websocket:push:session_*","push_method":"Redis通道桥接","timestamp":1755074938,"job_class":"App\\Jobs\\ProcessTextGeneration","job_method":"handle"} 
[2025-08-13 16:48:58] production.WARNING: 未找到任务记录 {"task_id":"text_gen_1755074936_NspwGeGP","user_id":14} 
[2025-08-13 16:48:58] production.INFO: WebSocket推送结果 - 任务完成 {"task_id":"text_gen_1755074936_NspwGeGP","user_id":14,"push_success":"no","push_method":"Redis通道桥接"} 
[2025-08-13 16:48:58] production.INFO: 文本生成任务完成 {"task_id":"text_gen_1755074936_NspwGeGP","user_id":14,"context":"text","ai_task_id":32,"cost":0} 
[2025-08-13 16:50:40] production.ERROR: There are no commands defined in the "config" namespace. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\NamespaceNotFoundException(code: 0): There are no commands defined in the \"config\" namespace. at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php:669)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(720): Symfony\\Component\\Console\\Application->findNamespace('config')
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(266): Symfony\\Component\\Console\\Application->find('config:show')
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Console\\Kernel.php(160): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\artisan(35): Laravel\\Lumen\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#5 {main}
"} 
[2025-08-13 17:00:10] production.INFO: 积分检查 {"user_id":14,"required_amount":0.5905,"current_balance":"92.06","sufficient":true,"business_type":"text_generation","business_id":null} 
[2025-08-13 17:00:10] production.INFO: 积分冻结成功 {"user_id":14,"amount":0.5905,"business_type":"text_generation","business_id":null,"transaction_id":33,"freeze_id":33} 
[2025-08-13 17:00:10] production.INFO: WebSocket文本生成 - 参数传递检查 {"task_id":"text_gen_1755075610_D6jFCAJs","user_id":14,"estimated_cost":0.5905,"generation_params":{"max_tokens":"400","temperature":"0.7","top_p":0.9,"estimated_cost":0.5905},"generation_params_keys":["max_tokens","temperature","top_p","estimated_cost"],"generation_params_count":4} 
[2025-08-13 17:00:10] production.INFO: WebSocket推送进度 - 任务开始 {"task_id":"text_gen_1755075610_D6jFCAJs","user_id":14,"progress":10,"message":"开始文本生成任务","push_type":"ai_generation_progress","redis_channel":"websocket:push:session_*","push_method":"Redis通道桥接","timestamp":1755075610,"job_class":"App\\Jobs\\ProcessTextGeneration","job_method":"handle"} 
[2025-08-13 17:00:10] production.WARNING: 未找到任务记录 {"task_id":"text_gen_1755075610_D6jFCAJs","user_id":14} 
[2025-08-13 17:00:10] production.INFO: WebSocket推送结果 - 任务开始 {"task_id":"text_gen_1755075610_D6jFCAJs","user_id":14,"push_success":"no","push_method":"Redis通道桥接"} 
[2025-08-13 17:00:10] production.INFO: WebSocket推送进度 - 连接AI平台 {"task_id":"text_gen_1755075610_D6jFCAJs","user_id":14,"progress":30,"message":"连接AI平台","push_type":"ai_generation_progress","redis_channel":"websocket:push:session_*","push_method":"Redis通道桥接","timestamp":1755075610,"job_class":"App\\Jobs\\ProcessTextGeneration","job_method":"handle"} 
[2025-08-13 17:00:10] production.WARNING: 未找到任务记录 {"task_id":"text_gen_1755075610_D6jFCAJs","user_id":14} 
[2025-08-13 17:00:10] production.INFO: WebSocket推送结果 - 连接AI平台 {"task_id":"text_gen_1755075610_D6jFCAJs","user_id":14,"push_success":"no","push_method":"Redis通道桥接"} 
[2025-08-13 17:00:10] production.INFO: 从WebSocket会话获取business_type {"task_id":"text_gen_1755075610_D6jFCAJs","user_id":14,"session_id":"ws_XeLhDmQCfkQrn6vNY7DA91L0UF3erivH","business_type":"text_generation_storyboard"} 
[2025-08-13 17:00:10] production.DEBUG: Lumen事务开始 {"context":"AiGenerationService.generateText","level":1,"action":"real_begin","framework":"Lumen 10"} 
[2025-08-13 17:00:10] production.INFO: AI服务调用 {"platform":"deepseek","mode":"mock","data_size":118} 
[2025-08-13 17:00:10] production.INFO: 调用模拟服务 {"platform":"deepseek","url":"https://aiapi.tiptop.cn/deepseek/chat/completions","timeout":"30"} 
[2025-08-13 17:00:10] production.INFO: 模拟服务调用成功 {"platform":"deepseek","status":200,"response_size":0} 
[2025-08-13 17:00:10] production.INFO: 用户偏好记录更新成功 {"user_id":14,"platform":"deepseek","task_type":"text_generation_storyboard","usage_count":1,"success_rate":1.0} 
[2025-08-13 17:00:10] production.INFO: 积分消费成功 {"transaction_id":33,"user_id":14,"amount":"0.59"} 
[2025-08-13 17:00:10] production.ERROR: AI文本生成失败，跳过后续处理 {"user_id":14,"task_id":33,"ai_response_code":200,"ai_response_message":"文本生成成功","has_text_content":false} 
[2025-08-13 17:00:10] production.WARNING: 事务回滚 {"context":"AiGenerationService.generateText","reason":"AI文本生成失败","original_level":1,"action":"real_rollback"} 
[2025-08-13 17:00:10] production.INFO: WebSocket推送进度 - 保存文本数据 {"task_id":"text_gen_1755075610_D6jFCAJs","user_id":14,"progress":80,"message":"保存文本数据","push_type":"ai_generation_progress","redis_channel":"websocket:push:session_*","push_method":"Redis通道桥接","timestamp":1755075610,"job_class":"App\\Jobs\\ProcessTextGeneration","job_method":"handle"} 
[2025-08-13 17:00:10] production.WARNING: 未找到任务记录 {"task_id":"text_gen_1755075610_D6jFCAJs","user_id":14} 
[2025-08-13 17:00:10] production.INFO: WebSocket推送结果 - 保存文本数据 {"task_id":"text_gen_1755075610_D6jFCAJs","user_id":14,"push_success":"no","push_method":"Redis通道桥接"} 
[2025-08-13 17:00:10] production.INFO: WebSocket推送进度 - 文本生成完成 {"task_id":"text_gen_1755075610_D6jFCAJs","user_id":14,"progress":100,"message":"文本生成完成","push_type":"ai_generation_progress"} 
[2025-08-13 17:00:10] production.WARNING: 未找到任务记录 {"task_id":"text_gen_1755075610_D6jFCAJs","user_id":14} 
[2025-08-13 17:00:10] production.INFO: WebSocket推送事件 - 任务完成 {"task_id":"text_gen_1755075610_D6jFCAJs","user_id":14,"push_type":"ai_generation_completed","redis_channel":"websocket:push:session_*","push_method":"Redis通道桥接","timestamp":1755075610,"job_class":"App\\Jobs\\ProcessTextGeneration","job_method":"handle"} 
[2025-08-13 17:00:11] production.WARNING: 未找到任务记录 {"task_id":"text_gen_1755075610_D6jFCAJs","user_id":14} 
[2025-08-13 17:00:11] production.INFO: WebSocket推送结果 - 任务完成 {"task_id":"text_gen_1755075610_D6jFCAJs","user_id":14,"push_success":"no","push_method":"Redis通道桥接"} 
[2025-08-13 17:00:11] production.INFO: 文本生成任务完成 {"task_id":"text_gen_1755075610_D6jFCAJs","user_id":14,"context":"text","ai_task_id":33,"cost":0} 
[2025-08-13 17:04:35] production.INFO: 积分检查 {"user_id":14,"required_amount":0.5894,"current_balance":"91.47","sufficient":true,"business_type":"text_generation","business_id":null} 
[2025-08-13 17:04:35] production.INFO: 积分冻结成功 {"user_id":14,"amount":0.5894,"business_type":"text_generation","business_id":null,"transaction_id":34,"freeze_id":34} 
[2025-08-13 17:04:35] production.INFO: WebSocket文本生成 - 参数传递检查 {"task_id":"text_gen_1755075875_7I7pQi74","user_id":14,"estimated_cost":0.5894,"generation_params":{"max_tokens":"400","temperature":"0.7","top_p":0.9,"estimated_cost":0.5894},"generation_params_keys":["max_tokens","temperature","top_p","estimated_cost"],"generation_params_count":4} 
[2025-08-13 17:04:36] production.INFO: WebSocket推送进度 - 任务开始 {"task_id":"text_gen_1755075875_7I7pQi74","user_id":14,"progress":10,"message":"开始文本生成任务","push_type":"ai_generation_progress","redis_channel":"websocket:push:session_*","push_method":"Redis通道桥接","timestamp":1755075876,"job_class":"App\\Jobs\\ProcessTextGeneration","job_method":"handle"} 
[2025-08-13 17:04:36] production.WARNING: 未找到任务记录 {"task_id":"text_gen_1755075875_7I7pQi74","user_id":14} 
[2025-08-13 17:04:36] production.INFO: WebSocket推送结果 - 任务开始 {"task_id":"text_gen_1755075875_7I7pQi74","user_id":14,"push_success":"no","push_method":"Redis通道桥接"} 
[2025-08-13 17:04:36] production.INFO: WebSocket推送进度 - 连接AI平台 {"task_id":"text_gen_1755075875_7I7pQi74","user_id":14,"progress":30,"message":"连接AI平台","push_type":"ai_generation_progress","redis_channel":"websocket:push:session_*","push_method":"Redis通道桥接","timestamp":1755075876,"job_class":"App\\Jobs\\ProcessTextGeneration","job_method":"handle"} 
[2025-08-13 17:04:36] production.WARNING: 未找到任务记录 {"task_id":"text_gen_1755075875_7I7pQi74","user_id":14} 
[2025-08-13 17:04:36] production.INFO: WebSocket推送结果 - 连接AI平台 {"task_id":"text_gen_1755075875_7I7pQi74","user_id":14,"push_success":"no","push_method":"Redis通道桥接"} 
[2025-08-13 17:04:36] production.INFO: 从WebSocket会话获取business_type {"task_id":"text_gen_1755075875_7I7pQi74","user_id":14,"session_id":"ws_XeLhDmQCfkQrn6vNY7DA91L0UF3erivH","business_type":"text_generation_storyboard"} 
[2025-08-13 17:04:36] production.DEBUG: Lumen事务开始 {"context":"AiGenerationService.generateText","level":1,"action":"real_begin","framework":"Lumen 10"} 
[2025-08-13 17:04:36] production.INFO: AI服务调用 {"platform":"deepseek","mode":"mock","data_size":118} 
[2025-08-13 17:04:36] production.INFO: 调用模拟服务 {"platform":"deepseek","url":"https://aiapi.tiptop.cn/deepseek/chat/completions","timeout":"30"} 
[2025-08-13 17:04:36] production.INFO: 模拟服务调用成功 {"platform":"deepseek","status":200,"response_size":0} 
[2025-08-13 17:04:36] production.INFO: 用户偏好记录更新成功 {"user_id":14,"platform":"deepseek","task_type":"text_generation_storyboard","usage_count":1,"success_rate":1.0} 
[2025-08-13 17:04:36] production.INFO: 积分消费成功 {"transaction_id":34,"user_id":14,"amount":"0.59"} 
[2025-08-13 17:04:36] production.ERROR: AI文本生成失败，跳过后续处理 {"user_id":14,"task_id":34,"ai_response_code":200,"ai_response_message":"文本生成成功","has_text_content":false} 
[2025-08-13 17:04:36] production.WARNING: 事务回滚 {"context":"AiGenerationService.generateText","reason":"AI文本生成失败","original_level":1,"action":"real_rollback"} 
[2025-08-13 17:04:36] production.INFO: WebSocket推送进度 - 保存文本数据 {"task_id":"text_gen_1755075875_7I7pQi74","user_id":14,"progress":80,"message":"保存文本数据","push_type":"ai_generation_progress","redis_channel":"websocket:push:session_*","push_method":"Redis通道桥接","timestamp":1755075876,"job_class":"App\\Jobs\\ProcessTextGeneration","job_method":"handle"} 
[2025-08-13 17:04:36] production.WARNING: 未找到任务记录 {"task_id":"text_gen_1755075875_7I7pQi74","user_id":14} 
[2025-08-13 17:04:36] production.INFO: WebSocket推送结果 - 保存文本数据 {"task_id":"text_gen_1755075875_7I7pQi74","user_id":14,"push_success":"no","push_method":"Redis通道桥接"} 
[2025-08-13 17:04:36] production.INFO: WebSocket推送进度 - 文本生成完成 {"task_id":"text_gen_1755075875_7I7pQi74","user_id":14,"progress":100,"message":"文本生成完成","push_type":"ai_generation_progress"} 
[2025-08-13 17:04:36] production.WARNING: 未找到任务记录 {"task_id":"text_gen_1755075875_7I7pQi74","user_id":14} 
[2025-08-13 17:04:36] production.INFO: WebSocket推送事件 - 任务完成 {"task_id":"text_gen_1755075875_7I7pQi74","user_id":14,"push_type":"ai_generation_completed","redis_channel":"websocket:push:session_*","push_method":"Redis通道桥接","timestamp":1755075876,"job_class":"App\\Jobs\\ProcessTextGeneration","job_method":"handle"} 
[2025-08-13 17:04:36] production.WARNING: 未找到任务记录 {"task_id":"text_gen_1755075875_7I7pQi74","user_id":14} 
[2025-08-13 17:04:36] production.INFO: WebSocket推送结果 - 任务完成 {"task_id":"text_gen_1755075875_7I7pQi74","user_id":14,"push_success":"no","push_method":"Redis通道桥接"} 
[2025-08-13 17:04:36] production.INFO: 文本生成任务完成 {"task_id":"text_gen_1755075875_7I7pQi74","user_id":14,"context":"text","ai_task_id":34,"cost":0} 
[2025-08-13 17:06:41] production.INFO: 积分检查 {"user_id":14,"required_amount":0.5915,"current_balance":"90.88","sufficient":true,"business_type":"text_generation","business_id":null} 
[2025-08-13 17:06:41] production.INFO: 积分冻结成功 {"user_id":14,"amount":0.5915,"business_type":"text_generation","business_id":null,"transaction_id":35,"freeze_id":35} 
[2025-08-13 17:06:41] production.INFO: WebSocket文本生成 - 参数传递检查 {"task_id":"text_gen_1755076001_cCpW7bkv","user_id":14,"estimated_cost":0.5915,"generation_params":{"max_tokens":"400","temperature":"0.7","top_p":0.9,"estimated_cost":0.5915},"generation_params_keys":["max_tokens","temperature","top_p","estimated_cost"],"generation_params_count":4} 
[2025-08-13 17:06:43] production.INFO: WebSocket推送进度 - 任务开始 {"task_id":"text_gen_1755076001_cCpW7bkv","user_id":14,"progress":10,"message":"开始文本生成任务","push_type":"ai_generation_progress","redis_channel":"websocket:push:session_*","push_method":"Redis通道桥接","timestamp":1755076003,"job_class":"App\\Jobs\\ProcessTextGeneration","job_method":"handle"} 
[2025-08-13 17:06:43] production.WARNING: 未找到任务记录 {"task_id":"text_gen_1755076001_cCpW7bkv","user_id":14} 
[2025-08-13 17:06:43] production.INFO: WebSocket推送结果 - 任务开始 {"task_id":"text_gen_1755076001_cCpW7bkv","user_id":14,"push_success":"no","push_method":"Redis通道桥接"} 
[2025-08-13 17:06:43] production.INFO: WebSocket推送进度 - 连接AI平台 {"task_id":"text_gen_1755076001_cCpW7bkv","user_id":14,"progress":30,"message":"连接AI平台","push_type":"ai_generation_progress","redis_channel":"websocket:push:session_*","push_method":"Redis通道桥接","timestamp":1755076003,"job_class":"App\\Jobs\\ProcessTextGeneration","job_method":"handle"} 
[2025-08-13 17:06:43] production.WARNING: 未找到任务记录 {"task_id":"text_gen_1755076001_cCpW7bkv","user_id":14} 
[2025-08-13 17:06:43] production.INFO: WebSocket推送结果 - 连接AI平台 {"task_id":"text_gen_1755076001_cCpW7bkv","user_id":14,"push_success":"no","push_method":"Redis通道桥接"} 
[2025-08-13 17:06:43] production.INFO: 从WebSocket会话获取business_type {"task_id":"text_gen_1755076001_cCpW7bkv","user_id":14,"session_id":"ws_XeLhDmQCfkQrn6vNY7DA91L0UF3erivH","business_type":"text_generation_storyboard"} 
[2025-08-13 17:06:43] production.DEBUG: Lumen事务开始 {"context":"AiGenerationService.generateText","level":1,"action":"real_begin","framework":"Lumen 10"} 
[2025-08-13 17:06:43] production.INFO: AI服务调用 {"platform":"deepseek","mode":"mock","data_size":118} 
[2025-08-13 17:06:43] production.INFO: 调用模拟服务 {"platform":"deepseek","url":"https://aiapi.tiptop.cn/deepseek/chat/completions","timeout":"30"} 
[2025-08-13 17:06:43] production.INFO: 模拟服务调用成功 {"platform":"deepseek","status":200,"response_size":0} 
[2025-08-13 17:06:43] production.INFO: 用户偏好记录更新成功 {"user_id":14,"platform":"deepseek","task_type":"text_generation_storyboard","usage_count":1,"success_rate":1.0} 
[2025-08-13 17:06:43] production.INFO: 积分消费成功 {"transaction_id":35,"user_id":14,"amount":"0.59"} 
[2025-08-13 17:06:43] production.ERROR: AI文本生成失败，跳过后续处理 {"user_id":14,"task_id":35,"ai_response_code":200,"ai_response_message":"文本生成成功","has_text_content":false} 
[2025-08-13 17:06:43] production.WARNING: 事务回滚 {"context":"AiGenerationService.generateText","reason":"AI文本生成失败","original_level":1,"action":"real_rollback"} 
[2025-08-13 17:06:43] production.INFO: WebSocket推送进度 - 保存文本数据 {"task_id":"text_gen_1755076001_cCpW7bkv","user_id":14,"progress":80,"message":"保存文本数据","push_type":"ai_generation_progress","redis_channel":"websocket:push:session_*","push_method":"Redis通道桥接","timestamp":1755076003,"job_class":"App\\Jobs\\ProcessTextGeneration","job_method":"handle"} 
[2025-08-13 17:06:43] production.WARNING: 未找到任务记录 {"task_id":"text_gen_1755076001_cCpW7bkv","user_id":14} 
[2025-08-13 17:06:43] production.INFO: WebSocket推送结果 - 保存文本数据 {"task_id":"text_gen_1755076001_cCpW7bkv","user_id":14,"push_success":"no","push_method":"Redis通道桥接"} 
[2025-08-13 17:06:43] production.INFO: WebSocket推送进度 - 文本生成完成 {"task_id":"text_gen_1755076001_cCpW7bkv","user_id":14,"progress":100,"message":"文本生成完成","push_type":"ai_generation_progress"} 
[2025-08-13 17:06:43] production.WARNING: 未找到任务记录 {"task_id":"text_gen_1755076001_cCpW7bkv","user_id":14} 
[2025-08-13 17:06:43] production.INFO: WebSocket推送事件 - 任务完成 {"task_id":"text_gen_1755076001_cCpW7bkv","user_id":14,"push_type":"ai_generation_completed","redis_channel":"websocket:push:session_*","push_method":"Redis通道桥接","timestamp":1755076003,"job_class":"App\\Jobs\\ProcessTextGeneration","job_method":"handle"} 
[2025-08-13 17:06:43] production.WARNING: 未找到任务记录 {"task_id":"text_gen_1755076001_cCpW7bkv","user_id":14} 
[2025-08-13 17:06:43] production.INFO: WebSocket推送结果 - 任务完成 {"task_id":"text_gen_1755076001_cCpW7bkv","user_id":14,"push_success":"no","push_method":"Redis通道桥接"} 
[2025-08-13 17:06:43] production.INFO: 文本生成任务完成 {"task_id":"text_gen_1755076001_cCpW7bkv","user_id":14,"context":"text","ai_task_id":35,"cost":0} 
[2025-08-13 17:08:24] production.INFO: 积分检查 {"user_id":14,"required_amount":0.5915,"current_balance":"90.29","sufficient":true,"business_type":"text_generation","business_id":null} 
[2025-08-13 17:08:24] production.INFO: 积分冻结成功 {"user_id":14,"amount":0.5915,"business_type":"text_generation","business_id":null,"transaction_id":36,"freeze_id":36} 
[2025-08-13 17:08:24] production.INFO: WebSocket文本生成 - 参数传递检查 {"task_id":"text_gen_1755076104_AyuRksT0","user_id":14,"estimated_cost":0.5915,"generation_params":{"max_tokens":"400","temperature":"0.7","top_p":0.9,"estimated_cost":0.5915},"generation_params_keys":["max_tokens","temperature","top_p","estimated_cost"],"generation_params_count":4} 
[2025-08-13 17:08:25] production.INFO: WebSocket推送进度 - 任务开始 {"task_id":"text_gen_1755076104_AyuRksT0","user_id":14,"progress":10,"message":"开始文本生成任务","push_type":"ai_generation_progress","redis_channel":"websocket:push:session_*","push_method":"Redis通道桥接","timestamp":1755076105,"job_class":"App\\Jobs\\ProcessTextGeneration","job_method":"handle"} 
[2025-08-13 17:08:25] production.WARNING: 未找到任务记录 {"task_id":"text_gen_1755076104_AyuRksT0","user_id":14} 
[2025-08-13 17:08:25] production.INFO: WebSocket推送结果 - 任务开始 {"task_id":"text_gen_1755076104_AyuRksT0","user_id":14,"push_success":"no","push_method":"Redis通道桥接"} 
[2025-08-13 17:08:25] production.INFO: WebSocket推送进度 - 连接AI平台 {"task_id":"text_gen_1755076104_AyuRksT0","user_id":14,"progress":30,"message":"连接AI平台","push_type":"ai_generation_progress","redis_channel":"websocket:push:session_*","push_method":"Redis通道桥接","timestamp":1755076105,"job_class":"App\\Jobs\\ProcessTextGeneration","job_method":"handle"} 
[2025-08-13 17:08:25] production.WARNING: 未找到任务记录 {"task_id":"text_gen_1755076104_AyuRksT0","user_id":14} 
[2025-08-13 17:08:25] production.INFO: WebSocket推送结果 - 连接AI平台 {"task_id":"text_gen_1755076104_AyuRksT0","user_id":14,"push_success":"no","push_method":"Redis通道桥接"} 
[2025-08-13 17:08:25] production.INFO: 从WebSocket会话获取business_type {"task_id":"text_gen_1755076104_AyuRksT0","user_id":14,"session_id":"ws_XeLhDmQCfkQrn6vNY7DA91L0UF3erivH","business_type":"text_generation_storyboard"} 
[2025-08-13 17:08:25] production.DEBUG: Lumen事务开始 {"context":"AiGenerationService.generateText","level":1,"action":"real_begin","framework":"Lumen 10"} 
[2025-08-13 17:08:25] production.INFO: AI服务调用 {"platform":"deepseek","mode":"mock","data_size":118} 
[2025-08-13 17:08:25] production.INFO: 调用模拟服务 {"platform":"deepseek","url":"https://aiapi.tiptop.cn/deepseek/chat/completions","timeout":"30"} 
[2025-08-13 17:08:25] production.INFO: 模拟服务调用成功 {"platform":"deepseek","status":200,"response_size":0} 
[2025-08-13 17:08:25] production.INFO: 用户偏好记录更新成功 {"user_id":14,"platform":"deepseek","task_type":"text_generation_storyboard","usage_count":1,"success_rate":1.0} 
[2025-08-13 17:08:26] production.INFO: 积分消费成功 {"transaction_id":36,"user_id":14,"amount":"0.59"} 
[2025-08-13 17:08:26] production.ERROR: AI文本生成失败，跳过后续处理 {"user_id":14,"task_id":36,"ai_response_code":200,"ai_response_message":"文本生成成功","has_text_content":false} 
[2025-08-13 17:08:26] production.WARNING: 事务回滚 {"context":"AiGenerationService.generateText","reason":"AI文本生成失败","original_level":1,"action":"real_rollback"} 
[2025-08-13 17:08:26] production.INFO: WebSocket推送进度 - 保存文本数据 {"task_id":"text_gen_1755076104_AyuRksT0","user_id":14,"progress":80,"message":"保存文本数据","push_type":"ai_generation_progress","redis_channel":"websocket:push:session_*","push_method":"Redis通道桥接","timestamp":1755076106,"job_class":"App\\Jobs\\ProcessTextGeneration","job_method":"handle"} 
[2025-08-13 17:08:26] production.WARNING: 未找到任务记录 {"task_id":"text_gen_1755076104_AyuRksT0","user_id":14} 
[2025-08-13 17:08:26] production.INFO: WebSocket推送结果 - 保存文本数据 {"task_id":"text_gen_1755076104_AyuRksT0","user_id":14,"push_success":"no","push_method":"Redis通道桥接"} 
[2025-08-13 17:08:26] production.INFO: WebSocket推送进度 - 文本生成完成 {"task_id":"text_gen_1755076104_AyuRksT0","user_id":14,"progress":100,"message":"文本生成完成","push_type":"ai_generation_progress"} 
[2025-08-13 17:08:26] production.WARNING: 未找到任务记录 {"task_id":"text_gen_1755076104_AyuRksT0","user_id":14} 
[2025-08-13 17:08:26] production.INFO: WebSocket推送事件 - 任务完成 {"task_id":"text_gen_1755076104_AyuRksT0","user_id":14,"push_type":"ai_generation_completed","redis_channel":"websocket:push:session_*","push_method":"Redis通道桥接","timestamp":1755076106,"job_class":"App\\Jobs\\ProcessTextGeneration","job_method":"handle"} 
[2025-08-13 17:08:26] production.WARNING: 未找到任务记录 {"task_id":"text_gen_1755076104_AyuRksT0","user_id":14} 
[2025-08-13 17:08:26] production.INFO: WebSocket推送结果 - 任务完成 {"task_id":"text_gen_1755076104_AyuRksT0","user_id":14,"push_success":"no","push_method":"Redis通道桥接"} 
[2025-08-13 17:08:26] production.INFO: 文本生成任务完成 {"task_id":"text_gen_1755076104_AyuRksT0","user_id":14,"context":"text","ai_task_id":36,"cost":0} 
[2025-08-13 17:19:48] production.INFO: 积分检查 {"user_id":14,"required_amount":0.5919,"current_balance":"89.70","sufficient":true,"business_type":"text_generation","business_id":null} 
[2025-08-13 17:19:48] production.INFO: 积分冻结成功 {"user_id":14,"amount":0.5919,"business_type":"text_generation","business_id":null,"transaction_id":37,"freeze_id":37} 
[2025-08-13 17:19:48] production.INFO: WebSocket文本生成 - 参数传递检查 {"task_id":"text_gen_1755076788_BWur0ezc","user_id":14,"estimated_cost":0.5919,"generation_params":{"max_tokens":"400","temperature":"0.7","top_p":0.9,"estimated_cost":0.5919},"generation_params_keys":["max_tokens","temperature","top_p","estimated_cost"],"generation_params_count":4} 
[2025-08-13 17:19:50] production.INFO: WebSocket推送进度 - 任务开始 {"task_id":"text_gen_1755076788_BWur0ezc","user_id":14,"progress":10,"message":"开始文本生成任务","push_type":"ai_generation_progress","redis_channel":"websocket:push:session_*","push_method":"Redis通道桥接","timestamp":1755076790,"job_class":"App\\Jobs\\ProcessTextGeneration","job_method":"handle"} 
[2025-08-13 17:19:50] production.WARNING: 未找到任务记录 {"task_id":"text_gen_1755076788_BWur0ezc","user_id":14} 
[2025-08-13 17:19:50] production.INFO: WebSocket推送结果 - 任务开始 {"task_id":"text_gen_1755076788_BWur0ezc","user_id":14,"push_success":"no","push_method":"Redis通道桥接"} 
[2025-08-13 17:19:50] production.INFO: WebSocket推送进度 - 连接AI平台 {"task_id":"text_gen_1755076788_BWur0ezc","user_id":14,"progress":30,"message":"连接AI平台","push_type":"ai_generation_progress","redis_channel":"websocket:push:session_*","push_method":"Redis通道桥接","timestamp":1755076790,"job_class":"App\\Jobs\\ProcessTextGeneration","job_method":"handle"} 
[2025-08-13 17:19:50] production.WARNING: 未找到任务记录 {"task_id":"text_gen_1755076788_BWur0ezc","user_id":14} 
[2025-08-13 17:19:50] production.INFO: WebSocket推送结果 - 连接AI平台 {"task_id":"text_gen_1755076788_BWur0ezc","user_id":14,"push_success":"no","push_method":"Redis通道桥接"} 
[2025-08-13 17:19:50] production.INFO: 从WebSocket会话获取business_type {"task_id":"text_gen_1755076788_BWur0ezc","user_id":14,"session_id":"ws_XeLhDmQCfkQrn6vNY7DA91L0UF3erivH","business_type":"text_generation_storyboard"} 
[2025-08-13 17:19:50] production.DEBUG: Lumen事务开始 {"context":"AiGenerationService.generateText","level":1,"action":"real_begin","framework":"Lumen 10"} 
[2025-08-13 17:19:50] production.INFO: AI服务调用 {"platform":"deepseek","mode":"mock","data_size":118} 
[2025-08-13 17:19:50] production.INFO: 调用模拟服务 {"platform":"deepseek","url":"https://aiapi.tiptop.cn/deepseek/chat/completions","timeout":"30"} 
[2025-08-13 17:19:50] production.INFO: 模拟服务调用成功 {"platform":"deepseek","status":200,"response_size":0} 
[2025-08-13 17:19:50] production.INFO: 用户偏好记录更新成功 {"user_id":14,"platform":"deepseek","task_type":"text_generation_storyboard","usage_count":1,"success_rate":1.0} 
[2025-08-13 17:19:50] production.INFO: 积分消费成功 {"transaction_id":37,"user_id":14,"amount":"0.59"} 
[2025-08-13 17:19:50] production.ERROR: AI文本生成失败，跳过后续处理 {"user_id":14,"task_id":37,"ai_response_code":200,"ai_response_message":"文本生成成功","has_text_content":false} 
[2025-08-13 17:19:50] production.WARNING: 事务回滚 {"context":"AiGenerationService.generateText","reason":"AI文本生成失败","original_level":1,"action":"real_rollback"} 
[2025-08-13 17:19:50] production.INFO: WebSocket推送进度 - 保存文本数据 {"task_id":"text_gen_1755076788_BWur0ezc","user_id":14,"progress":80,"message":"保存文本数据","push_type":"ai_generation_progress","redis_channel":"websocket:push:session_*","push_method":"Redis通道桥接","timestamp":1755076790,"job_class":"App\\Jobs\\ProcessTextGeneration","job_method":"handle"} 
[2025-08-13 17:19:50] production.WARNING: 未找到任务记录 {"task_id":"text_gen_1755076788_BWur0ezc","user_id":14} 
[2025-08-13 17:19:50] production.INFO: WebSocket推送结果 - 保存文本数据 {"task_id":"text_gen_1755076788_BWur0ezc","user_id":14,"push_success":"no","push_method":"Redis通道桥接"} 
[2025-08-13 17:19:50] production.INFO: WebSocket推送进度 - 文本生成完成 {"task_id":"text_gen_1755076788_BWur0ezc","user_id":14,"progress":100,"message":"文本生成完成","push_type":"ai_generation_progress"} 
[2025-08-13 17:19:50] production.WARNING: 未找到任务记录 {"task_id":"text_gen_1755076788_BWur0ezc","user_id":14} 
[2025-08-13 17:19:50] production.INFO: WebSocket推送事件 - 任务完成 {"task_id":"text_gen_1755076788_BWur0ezc","user_id":14,"push_type":"ai_generation_completed","redis_channel":"websocket:push:session_*","push_method":"Redis通道桥接","timestamp":1755076790,"job_class":"App\\Jobs\\ProcessTextGeneration","job_method":"handle"} 
[2025-08-13 17:19:50] production.WARNING: 未找到任务记录 {"task_id":"text_gen_1755076788_BWur0ezc","user_id":14} 
[2025-08-13 17:19:50] production.INFO: WebSocket推送结果 - 任务完成 {"task_id":"text_gen_1755076788_BWur0ezc","user_id":14,"push_success":"no","push_method":"Redis通道桥接"} 
[2025-08-13 17:19:50] production.INFO: 文本生成任务完成 {"task_id":"text_gen_1755076788_BWur0ezc","user_id":14,"context":"text","ai_task_id":37,"cost":0} 
[2025-08-13 17:29:20] production.INFO: User offline {"user_id":14} 
[2025-08-13 21:19:47] production.ERROR: Redis通道订阅失败 {"error":"read error on connection to 127.0.0.1:6379"} 
[2025-08-13 21:24:42] production.INFO: WebSocket业务类型映射 {"frontend_business_type":"storyboard","mapped_task_type":"text_generation_storyboard","user_id":14,"session_id":"ws_UnIOrQEmAQCjzVMFPGXwb7R5f1iuYjAI"} 
[2025-08-13 21:24:42] production.INFO: WebSocket连接认证成功 {"session_id":"ws_UnIOrQEmAQCjzVMFPGXwb7R5f1iuYjAI","user_id":14,"client_type":"python_tool","connection_ip":"127.0.0.1"} 
[2025-08-13 21:26:31] production.INFO: 积分检查 {"user_id":14,"required_amount":1.4137,"current_balance":"89.11","sufficient":true,"business_type":"text_generation","business_id":null} 
[2025-08-13 21:26:31] production.INFO: 积分冻结成功 {"user_id":14,"amount":1.4137,"business_type":"text_generation","business_id":null,"transaction_id":38,"freeze_id":38} 
[2025-08-13 21:26:31] production.INFO: WebSocket文本生成 - 参数传递检查 {"task_id":"text_gen_1755091591_URCXUItt","user_id":14,"estimated_cost":1.4137,"generation_params":{"max_tokens":"1000","temperature":"0.7","top_p":0.9,"estimated_cost":1.4137},"generation_params_keys":["max_tokens","temperature","top_p","estimated_cost"],"generation_params_count":4} 
[2025-08-13 21:28:19] production.ERROR: Command "tinker" is not defined. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\CommandNotFoundException(code: 0): Command \"tinker\" is not defined. at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php:737)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(266): Symfony\\Component\\Console\\Application->find('tinker')
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Console\\Kernel.php(160): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\artisan(35): Laravel\\Lumen\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 {main}
"} 
