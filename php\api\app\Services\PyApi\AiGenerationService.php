<?php

namespace App\Services\PyApi;

use App\Jobs\ProcessTextGeneration;
use App\Helpers\LogCheckHelper;
use App\Enums\ApiCodeEnum;
use App\Models\AiModelConfig;
use App\Models\AiGenerationTask;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Carbon\Carbon;
use App\Services\Common\TransactionManager;

/**
 * AI生成服务
 */
class AiGenerationService
{
    protected $aiModelService;
    protected $pointsService;

    public function __construct(AiModelService $aiModelService, PointsService $pointsService)
    {
        $this->aiModelService = $aiModelService;
        $this->pointsService = $pointsService;
    }

    // 创建异步任务（在这里执行异步任务前冻结积分）
    public function generateTextWithWebSocket($request, $userId)
    {
        try {
            // 验证WebSocket会话ID
            $webSocketSessionId = $request->get('websocket_session_id');
            $webSocketService = app(WebSocketService::class);
            $sessionValid = $webSocketService->validateSession($webSocketSessionId, $userId);
            if (!$sessionValid) 
            {
                return [
                    'code' => ApiCodeEnum::VALIDATION_ERROR,
                    'message' => 'WebSocket会话无效',
                    'data' => []
                ];
            }

            // 验证项目ID和项目拥有者
            $projectId = $request->get('project_id');
            if ($projectId) {
                $project = \App\Models\Project::find($projectId);
                if (!$project) 
                {
                    return [
                        'code' => ApiCodeEnum::NOT_FOUND,
                        'message' => '指定的项目不存在',
                        'data' => []
                    ];
                }

                // 验证用户是否为项目的拥有者
                if ($project->user_id !== $userId) 
                {
                    return [
                        'code' => ApiCodeEnum::FORBIDDEN,
                        'message' => '您没有权限访问该项目',
                        'data' => []
                    ];
                }
            }

            // 🎯 积分预检查需要的生成参数
            $generationParams = [
                'max_tokens' => $request->get('max_tokens', 1000),
                'temperature' => $request->get('temperature', 0.7),
                'top_p' => $request->get('top_p', 0.9)
            ];

            // 🎯 调用服务层进行积分预检查
            $pointsCheckResult = $this->checkPointsForWebSocketTextGeneration(
                $userId,
                $request->get('prompt'),
                $request->get('model_id'),
                $generationParams
            );

            // 积分检查失败，直接返回错误，不创建异步任务
            if ($pointsCheckResult['code'] !== ApiCodeEnum::SUCCESS) 
            {
                return [
                    'code' => $pointsCheckResult['code'],
                    'message' => $pointsCheckResult['message'],
                    'data' => $pointsCheckResult['data'] ?? []
                ];
            }

            // 所需积分
            $estimatedCost = $pointsCheckResult['data']['estimated_cost'];

            // 冻结积分
            $freezeResult = $this->pointsService->freezePoints(
                $userId,
                $estimatedCost,
                'text_generation',
                null,
                300 // 5分钟超时
            );

            // 积分冻结失败时，回滚事务并返回错误，阻止任务创建
            if ($freezeResult['code'] !== ApiCodeEnum::SUCCESS) {
                Log::warning('积分冻结失败，阻止任务创建', [
                    'user_id' => $userId,
                    'estimated_cost' => $estimatedCost,
                    'freeze_result' => $freezeResult
                ]);
                return $freezeResult;
            }

            // 积分充足，创建异步任务
            $taskId = 'text_gen_' . time() . '_' . Str::random(8);
            $prompt = $request->get('prompt');
            $modelId = $request->get('model_id');

            // 上下文（设置AI扮演角色的作用）
            $context = $request->get('context', 'prompt_edit');

            // 预算积分
            $generationParams['estimated_cost'] = $estimatedCost;

            // 🔍 调试日志：检查参数传递
            Log::info('WebSocket文本生成 - 参数传递检查', [
                'task_id' => $taskId,
                'user_id' => $userId,
                'estimated_cost' => $estimatedCost,
                'generation_params' => $generationParams,
                'generation_params_keys' => array_keys($generationParams),
                'generation_params_count' => count($generationParams)
            ]);

            // 创建异步任务(ProcessTextGeneration)->AiGenerationService.generateText
            dispatch(new ProcessTextGeneration(
                $taskId,
                $userId,
                $prompt,
                $modelId,
                $request->get('project_id'),
                $request->get('storyboard_id'),
                $generationParams,
                $context
            ));

            // 返回任务信息
            $responseData = [
                'task_id' => $taskId,
                'status' => 'processing',
                'context' => $context,
                'estimated_cost' => $estimatedCost,
                'prompt' => substr($prompt, 0, 100),
                'generation_params' => $generationParams,
                'websocket_session_id' => $webSocketSessionId,
                'timestamp' => \Carbon\Carbon::now()->format('c'),
                'request_id' => 'req_' . Str::random(16)
            ];

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '文本生成任务创建成功',
                'data' => $responseData
            ];
        } 
        catch (\Exception $e)
        {
            $error_context = [
                'user_id' => $userId,
                'prompt' => substr($request->get('prompt'), 0, 100),
                'model_id' => $request->get('model_id'),
                'project_id' => $request->get('project_id')
            ];

            Log::error('文本生成任务创建失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '文本生成任务创建失败',
                'data' => null
            ];
        }
    }

    /**
     * 生成文本
     *
     * 🚨 架构边界规范：
     * ✅ 工具API接口服务负责调用AI服务，不进行模拟
     * ✅ 使用 AiServiceClient 实现环境切换
     * ❌ 不在工具API中进行模拟返回数据
     *
     * 🚨 业务流程：
     * 1. 解析提示词中的项目参数（比例、风格）
     * 2. 根据参数创建或关联项目
     * 3. 获取或创建AI模型配置
     * 4. 预扣积分并创建生成任务
     * 5. 此服务只支持WebSocket异步执行文本生成
     *
     * @param int $userId 用户ID
     * @param string $prompt 生成提示词（可能包含JSON格式的项目参数）
     * @param int|null $modelId AI模型配置ID，为空时使用默认模型
     * @param int|null $projectId 项目ID，为空时根据提示词参数创建项目
     * @param array $generationParams 生成参数（temperature、max_tokens等）
     * @param string|null $businessType 业务类型（php\api\config\ai.php business_type_mappings 中的后端任务类型，映射后的类型）
     *
     * @return array 返回结果数组
     *   - code: 状态码（ApiCodeEnum）
     *   - message: 提示信息
     *   - data: 任务数据
     *     - task_id: 任务ID
     *     - status: 任务状态
     *     - estimated_time: 预估完成时间
     *     - project_id: 项目ID（如果有）
     *
     * @throws \Exception 当业务逻辑异常时抛出 
     */
    public function generateText(
        int $userId, 
        string $prompt, 
        ?int $modelId = null, 
        ?int $projectId = null, 
        ?int $storyboardId = null, 
        array $generationParams = [], 
        ?string $businessType = null, 
        ?string $externalTaskId = null, 
        ): array
    {
        try {
            TransactionManager::begin('AiGenerationService.generateText');

            // 确定任务类型：优先使用传递的businessType，否则使用默认值
            $taskType = $businessType ?: AiGenerationTask::TYPE_TEXT_GENERATION;

            // 从$prompt中提取比例
            $promptData = json_decode($prompt, true);

            // 从$prompt中提取比例
            $aspectRatio = $promptData['比例'] ?? '';

            // 从$prompt中提取风格
            $styleId = $promptData['风格'] ?? '';

            // 从$prompt中提取提示词
            $prompt = $promptData['提示词'] ?? '';

            // 获取模型配置
            if ($modelId) 
            {
                $model = AiModelConfig::active()->find($modelId);
            } 
            else 
            {
                $model = $this->aiModelService->getUserDefaultModel($userId, AiModelConfig::TYPE_TEXT_GENERATION);
            }

            if (!$model) 
            {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '没有可用的文本生成模型',
                    'data' => []
                ];
            }

            // 检查模型健康状态
            if (!$model->isHealthy()) {
                return [
                    'code' => ApiCodeEnum::SERVICE_UNAVAILABLE,
                    'message' => '模型服务当前不可用',
                    'data' => []
                ];
            }

            // 🔍 调试日志：检查generateText方法中的参数
            Log::info('generateText方法 - 参数检查', [
                'user_id' => $userId,
                'external_task_id' => $externalTaskId,
                'generation_params' => $generationParams,
                'generation_params_keys' => array_keys($generationParams),
                'generation_params_count' => count($generationParams),
                'has_estimated_cost' => isset($generationParams['estimated_cost']),
                'estimated_cost_value' => $generationParams['estimated_cost'] ?? 'NOT_SET'
            ]);

            // 验证必需的参数
            if (!isset($generationParams['estimated_cost'])) {
                Log::error('generateText方法 - estimated_cost参数缺失', [
                    'user_id' => $userId,
                    'external_task_id' => $externalTaskId,
                    'generation_params' => $generationParams,
                    'generation_params_keys' => array_keys($generationParams)
                ]);
                throw new \InvalidArgumentException('estimated_cost参数是必需的，但在generationParams中未找到');
            }

            // 创建生成任务
            $task = AiGenerationTask::create([
                'user_id' => $userId,
                'project_id' => $projectId,
                'model_config_id' => $model->id,
                'task_type' => $taskType,
                'platform' => $model->platform,
                'model_name' => $model->model_name,
                'status' => AiGenerationTask::STATUS_PENDING,
                'input_data' => [
                    'prompt' => $prompt,
                    'max_tokens' => $generationParams['max_tokens'] ?? 1000,
                    'temperature' => $generationParams['temperature'] ?? 0.7,
                    'top_p' => $generationParams['top_p'] ?? 0.9
                ],
                'generation_params' => $generationParams,
                'external_task_id' => $externalTaskId,
                'cost' => $generationParams['estimated_cost']
            ]);

            // 执行生成任务
            $aiTextData = $this->executeTextGeneration($task);

            // 项目更新、分镜入库、生图提示词修改
            if($aiTextData['code'] === ApiCodeEnum::SUCCESS &&
               isset($aiTextData['data']['result']['text']) &&
               !empty(trim($aiTextData['data']['result']['text'])))
            {
                // 导入分镜服务层
                $ProjectStoryboardService = new \App\Services\PyApi\ProjectStoryboardService();

                // 根据以下类型对项目或分镜进行更新
                // 1、更新项目
                if($taskType == 'text_generation_storyboard')
                {
                    // 数据验证是否符合更新项目需求
                    if($projectId && ($aspectRatio || $styleId))
                    {
                        if($aspectRatio && $styleId)
                        {
                            // 更新项目故事标题、故事简概、风格、规格
                            // 传递 useTransaction=false 避免嵌套事务
                            $result = $ProjectStoryboardService->extractStoryboardsFromStory($userId, $projectId, $styleId, $aspectRatio, $aiTextData['data']['result']['text'], false);

                            if($result['code'] === ApiCodeEnum::SUCCESS)
                            {
                                $result['message'] = '分镜成功';
                            }
                            else
                            {
                                $result = [
                                    'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                                    'message' => '分镜失败（错误日志标记：'.$result['message'].'）',
                                    'data' => null
                                ];
                            }
                        }
                        else
                        {
                            Log::warning('text_generation_storyboard类型任务缺少必要参数', [
                                'user_id' => $userId,
                                'project_id' => $projectId,
                                'aspect_ratio' => $aspectRatio,
                                'style_id' => $styleId,
                                'task_type' => $taskType
                            ]);

                            // 如果需要前端收到错误信息，可以抛出异常或返回错误
                            DB::rollBack();
                            return [
                                'code' => ApiCodeEnum::VALIDATION_ERROR,
                                'message' => 'text_generation_storyboard类型任务缺少必要参数：需要同时提供aspect_ratio和style_id',
                                'data' => [
                                    'missing_params' => [
                                        'aspect_ratio' => $aspectRatio ? '已提供' : '缺失',
                                        'style_id' => $styleId ? '已提供' : '缺失'
                                    ]
                                ]
                            ];
                        }
                    }
                    else
                    {
                        Log::warning('text_generation_storyboard类型任务缺少项目ID或样式参数', [
                            'user_id' => $userId,
                            'project_id' => $projectId,
                            'aspect_ratio' => $aspectRatio,
                            'style_id' => $styleId,
                            'task_type' => $taskType
                        ]);
                    }
                }

                // 2、更新项目故事
                if($taskType == 'text_generation')
                {
                    // 1、故事扩写内容更新到 p_projects.story_content
                    if($projectId)
                    {
                        // 使用 ProjectService 更新项目故事内容
                        $projectService = new \App\Services\PyApi\ProjectService();
                        $result = $projectService->updateProject($projectId, $userId, [
                            'story_content' => $aiTextData['data']['result']['text']
                        ]);

                        if($result['code'] === ApiCodeEnum::SUCCESS)
                        {
                            $result['message'] = '故事智能扩写成功';
                        }
                        else
                        {
                            $result = [
                                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                                'message' => '故事智能扩写失败（错误日志标记：'.$result['message'].'）',
                                'data' => null
                            ];
                        }
                    }
                    else
                    {
                        Log::warning('text_generation类型任务缺少项目ID', [
                            'user_id' => $userId,
                            'task_type' => $taskType,
                            'prompt_data' => $promptData
                        ]);
                    }
                }

                // 3、更新生图提示词
                if($taskType == 'text_generation_prompt')
                {
                    // 1、提示词扩写内容更新到 p_storyboards.ai_prompt

                    if($storyboardId)
                    {
                        // 使用 ProjectStoryboardService 更新分镜AI提示词
                        $storyboardService = new \App\Services\PyApi\ProjectStoryboardService();
                        $result = $storyboardService->updateStoryboard($storyboardId, $userId, [
                            'ai_prompt' => $aiTextData['data']['result']['text']
                        ]);

                        if($result['code'] === ApiCodeEnum::SUCCESS)
                        {
                            $result['message'] = '分镜图片提示词智能扩写成功';
                        }
                        else
                        {
                            $result = [
                                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                                'message' => '分镜失败（错误日志标记：'.$result['message'].'）',
                                'data' => null
                            ];
                        }
                    }
                    else
                    {
                        Log::warning('text_generation_prompt类型任务缺少分镜ID', [
                            'user_id' => $userId,
                            'task_type' => $taskType,
                            'prompt_data' => $promptData
                        ]);
                    }
                }
            }
            else
            {
                // AI文本生成失败的处理
                Log::error('AI文本生成失败，跳过后续处理', [
                    'user_id' => $userId,
                    'task_id' => $task->id,
                    'ai_response_code' => $aiTextData['code'] ?? 'unknown',
                    'ai_response_message' => $aiTextData['message'] ?? 'unknown',
                    'has_text_content' => isset($aiTextData['data']['result']['text']) && !empty(trim($aiTextData['data']['result']['text']))
                ]);

                // 如果AI生成失败，直接返回失败结果，不执行后续的项目更新逻辑
                TransactionManager::rollback('AiGenerationService.generateText', 'AI文本生成失败');
                return $aiTextData;
            }

            Log::info('文本生成任务创建成功', [
                'task_id' => $task->id,
                'user_id' => $userId,
                'model_id' => $model->id,
                'estimated_cost' => $generationParams['estimated_cost']
            ]);

            TransactionManager::commit('AiGenerationService.generateText');

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '文本生成任务已创建',
                'data' => [
                    'task_id' => $task->id,
                    'status' => $task->status,
                    'estimated_cost' => $generationParams['estimated_cost'],
                    'model_name' => $model->model_name,
                    'platform' => $model->platform
                ]
            ];
        }
        catch (\Exception $e)
        {
            TransactionManager::rollback('AiGenerationService.generateText', $e->getMessage());

            $error_context = [
                'user_id' => $userId,
                'prompt' => substr($prompt, 0, 100),
                'model_id' => $modelId,
                'project_id' => $projectId,
                'generation_params_count' => is_array($generationParams) ? count($generationParams) : 0,
            ];

            Log::error('文本生成任务创建失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '文本生成任务创建失败',
                'data' => null
            ];
        }
    }

    /**
     * 获取任务状态
     */
    public function getTaskStatus(int $taskId, int $userId): array
    {
        try {
            $task = AiGenerationTask::byUser($userId)->find($taskId);
            
            if (!$task) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '任务不存在',
                    'data' => []
                ];
            }

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => 'success',
                'data' => [
                    'id' => $task->id,
                    'task_type' => $task->task_type,
                    'status' => $task->status,
                    'platform' => $task->platform,
                    'model_name' => $task->model_name,
                    'input_data' => $task->input_data,
                    'output_data' => $task->output_data,
                    'cost' => $task->cost,
                    'tokens_used' => $task->tokens_used,
                    'processing_time_ms' => $task->processing_time_ms,
                    'error_message' => $task->error_message,
                    'retry_count' => $task->retry_count,
                    'created_at' => $task->created_at->format('Y-m-d H:i:s'),
                    'started_at' => $task->started_at?->format('Y-m-d H:i:s'),
                    'completed_at' => $task->completed_at?->format('Y-m-d H:i:s')
                ]
            ];

        } catch (\Exception $e) {
            $error_context = [
                'task_id' => $taskId,
                'user_id' => $userId,
            ];

            Log::error('获取任务状态失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '获取任务状态失败',
                'data' => null
            ];
        }
    }

    /**
     * 获取用户任务列表
     */
    public function getUserTasks(int $userId, array $filters, int $page, int $perPage): array
    {
        try {
            $query = AiGenerationTask::byUser($userId);
            
            // 应用筛选条件
            if (!empty($filters['task_type'])) {
                $query->byType($filters['task_type']);
            }
            
            if (!empty($filters['status'])) {
                $query->byStatus($filters['status']);
            }
            
            if (!empty($filters['platform'])) {
                $query->byPlatform($filters['platform']);
            }
            
            $tasks = $query->orderBy('created_at', 'desc')
                ->paginate($perPage, ['*'], 'page', $page);

            $tasksData = $tasks->map(function ($task) {
                return [
                    'id' => $task->id,
                    'task_type' => $task->task_type,
                    'status' => $task->status,
                    'platform' => $task->platform,
                    'model_name' => $task->model_name,
                    'cost' => $task->cost,
                    'tokens_used' => $task->tokens_used,
                    'processing_time_ms' => $task->processing_time_ms,
                    'created_at' => $task->created_at->format('Y-m-d H:i:s'),
                    'completed_at' => $task->completed_at?->format('Y-m-d H:i:s')
                ];
            });

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => 'success',
                'data' => [
                    'tasks' => $tasksData,
                    'pagination' => [
                        'current_page' => $tasks->currentPage(),
                        'total' => $tasks->total(),
                        'per_page' => $tasks->perPage(),
                        'last_page' => $tasks->lastPage()
                    ]
                ]
            ];

        } catch (\Exception $e) {
            $error_context = [
                'user_id' => $userId,
                'filters' => $filters,
                'page' => $page,
                'per_page' => $perPage,
            ];

            Log::error('获取用户任务列表失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '获取任务列表失败',
                'data' => null
            ];
        }
    }

    /**
     * 重试任务
     */
    public function retryTask(int $taskId, int $userId): array
    {
        try {
            $task = AiGenerationTask::byUser($userId)->find($taskId);
            
            if (!$task) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '任务不存在',
                    'data' => []
                ];
            }

            if (!$task->canRetry()) {
                return [
                    'code' => ApiCodeEnum::INVALID_OPERATION,
                    'message' => '任务无法重试',
                    'data' => []
                ];
            }

            $task->incrementRetry();

            // 根据任务类型重新执行任务
            $taskType = $task->task_type;

            // 判断是否为文本生成类型的任务
            $textGenerationTypes = [
                AiGenerationTask::TYPE_TEXT_GENERATION,
                AiGenerationTask::TYPE_TEXT_GENERATION_PROMPT,
                AiGenerationTask::TYPE_TEXT_GENERATION_STORY,
                AiGenerationTask::TYPE_TEXT_GENERATION_STORYBOARD,
                AiGenerationTask::TYPE_CHARACTER_GENERATION
            ];

            if (in_array($taskType, $textGenerationTypes)) {
                $this->executeTextGeneration($task);
            } else {
                // 其他类型的任务暂不支持重试
                throw new \Exception("暂不支持重试任务类型: {$taskType}");
            }

            Log::info('任务重试成功', [
                'task_id' => $taskId,
                'user_id' => $userId,
                'retry_count' => $task->retry_count
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '任务重试成功',
                'data' => [
                    'task_id' => $task->id,
                    'status' => $task->status,
                    'retry_count' => $task->retry_count
                ]
            ];

        } catch (\Exception $e) {
            $error_context = [
                'task_id' => $taskId,
                'user_id' => $userId,
            ];

            Log::error('任务重试失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '任务重试失败',
                'data' => null
            ];
        }
    }

    /**
     * 执行文本生成（调用AI服务）
     *
     * 🚨 架构边界规范：
     * ✅ 工具API接口服务负责调用AI服务，不进行模拟
     * ✅ 使用 AiServiceClient 实现环境切换
     * ❌ 不在工具API中进行模拟返回数据
     *
     * 🚨 升级：支持用户平台选择和偏好记录
     */
    private function executeTextGeneration(AiGenerationTask $task): array
    {
        try {
            Log::info('🔍 开始执行文本生成任务', [
                'task_id' => $task->id,
                'user_id' => $task->user_id,
                'platform' => $task->platform,
                'model_name' => $task->model_name,
                'task_type' => $task->task_type,
                'external_task_id' => $task->external_task_id
            ]);

            $task->start();

            $requestData = [
                'model' => $task->model_name,
                'messages' => [
                    [
                        'role' => 'user',
                        'content' => $task->getInputData('prompt')
                    ]
                ],
                'max_tokens' => $task->getInputData('max_tokens', 1000),
                'temperature' => $task->getInputData('temperature', 0.7),
                'top_p' => $task->getInputData('top_p', 0.9)
            ];

            Log::info('🔍 准备调用AI服务', [
                'task_id' => $task->id,
                'platform' => $task->platform,
                'task_type' => $task->task_type,
                'request_data' => [
                    'model' => $requestData['model'],
                    'max_tokens' => $requestData['max_tokens'],
                    'temperature' => $requestData['temperature'],
                    'top_p' => $requestData['top_p'],
                    'prompt_length' => strlen($requestData['messages'][0]['content'])
                ]
            ]);

            // 🚨 升级：使用 callWithUserChoice 方法，支持用户偏好记录
            $response = \App\Services\AiServiceClient::callWithUserChoice(
                $task->platform,
                $task->task_type,
                $requestData,
                $task->user_id
            );

            Log::info('🔍 AI服务调用完成', [
                'task_id' => $task->id,
                'success' => $response['success'] ?? false,
                'has_data' => isset($response['data']),
                'has_error' => isset($response['error']),
                'response_keys' => array_keys($response)
            ]);

            if ($response['success']) {
                Log::info('🔍 AI服务调用成功，开始处理响应', [
                    'task_id' => $task->id,
                    'response_data_keys' => array_keys($response['data'] ?? [])
                ]);

                $aiResponse = $response['data'];

                // 解析AI服务响应
                $generatedText = $aiResponse['choices'][0]['message']['content'] ?? '';
                $tokensUsed = $aiResponse['usage']['total_tokens'] ?? 0;

                Log::info('🔍 AI响应解析完成', [
                    'task_id' => $task->id,
                    'generated_text_length' => strlen($generatedText),
                    'tokens_used' => $tokensUsed,
                    'has_choices' => isset($aiResponse['choices']),
                    'has_usage' => isset($aiResponse['usage'])
                ]);

                $outputData = [
                    'text' => $generatedText,
                    'finish_reason' => $aiResponse['choices'][0]['finish_reason'] ?? 'stop',
                    'model' => $aiResponse['model'] ?? $task->model_name,
                    'usage' => $aiResponse['usage'] ?? []
                ];

                $task->complete($outputData, $tokensUsed);

                Log::info('🔍 任务状态更新为完成', [
                    'task_id' => $task->id,
                    'status' => $task->status,
                    'completed_at' => $task->completed_at
                ]);

                // 消费冻结积分
                $this->pointsService->consumePoints($task->id);

                Log::info('🔍 积分消费完成', [
                    'task_id' => $task->id
                ]);

                // 返回数据
                $responseData = [
                    'task_id' => $task->id,                                    // 任务ID
                    'status' => 'completed',                                   // 任务状态：已完成
                    'result' => [                                              // 生成结果
                        'text' => $generatedText,                             // AI生成的文本内容
                        'finish_reason' => $outputData['finish_reason'],      // 完成原因（stop/length/content_filter等）
                        'model' => $outputData['model'],                      // 使用的AI模型名称
                        'usage' => $outputData['usage'],                      // Token使用统计信息
                        'tokens_used' => $tokensUsed                          // 总Token消耗数量
                    ],
                    'project_id' => $task->project_id,                        // 关联的项目ID（可为null）
                    'context' => $task->context,                              // 生成上下文（prompt_edit等）
                    'processing_time_ms' => $task->processing_time_ms,        // 处理耗时（毫秒）
                    'timestamp' => Carbon::now()->format('c')                 // 完成时间戳（ISO 8601格式）
                ];

                return [
                    'code' => ApiCodeEnum::SUCCESS,
                    'message' => '文本生成成功',
                    'data' => $responseData
                ];
            } else {
                throw new \Exception($response['error'] ?? 'AI服务调用失败');
            }

        } catch (\Exception $e) {
            $task->fail($e->getMessage());

            // 释放积分
            $this->pointsService->releasePoints($task->id);

            Log::error('文本生成任务失败', [
                'task_id' => $task->id,
                'platform' => $task->platform,
                'error' => $e->getMessage()
            ]);

            // 返回失败数据
            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '文本生成失败',
                'data' => [
                    'task_id' => $task->id,
                    'status' => 'failed',
                    'error' => $e->getMessage(),
                    'timestamp' => Carbon::now()->format('c')
                ]
            ];
        }
    }





    /**
     * WebSocket文本生成积分预检查
     * 在创建异步任务之前检查积分是否充足
     *
     * @param int $userId 用户ID
     * @param string $prompt 提示词
     * @param int|null $modelId 模型ID
     * @param array $generationParams 生成参数
     * @return array 检查结果
     */
    public function checkPointsForWebSocketTextGeneration(int $userId, string $prompt, ?int $modelId = null, array $generationParams = []): array
    {
        try {
            // 解析prompt获取实际提示词
            $promptData = json_decode($prompt, true);
            $actualPrompt = $promptData['提示词'] ?? $prompt;

            // 获取模型配置
            if ($modelId) {
                $model = AiModelConfig::active()->find($modelId);
            } else {
                $model = $this->aiModelService->getUserDefaultModel($userId, AiModelConfig::TYPE_TEXT_GENERATION);
            }

            if (!$model) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '没有可用的文本生成模型',
                    'data' => []
                ];
            }

            // 计算预估成本
            $estimatedCost = $this->calculateTextGenerationCost($model, $actualPrompt, $generationParams);

            // 检查积分是否充足
            $pointsCheck = $this->pointsService->checkPoints($userId, $estimatedCost, 'text_generation');

            if ($pointsCheck['code'] !== ApiCodeEnum::SUCCESS || !$pointsCheck['data']['sufficient']) {
                return [
                    'code' => ApiCodeEnum::INSUFFICIENT_POINTS,
                    'message' => '积分不足',
                    'data' => $pointsCheck['data'] ?? []
                ];
            }

            // 积分检查通过
            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '积分检查通过',
                'data' => [
                    'estimated_cost' => $estimatedCost,
                    'model' => $model,
                    'available_points' => $pointsCheck['data']['current_balance']
                ]
            ];

        } catch (\Exception $e) {
            Log::error('WebSocket文本生成积分预检查失败', [
                'user_id' => $userId,
                'model_id' => $modelId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::INTERNAL_ERROR,
                'message' => '积分检查失败：' . $e->getMessage(),
                'data' => []
            ];
        }
    }

    /**
     * 计算文本生成成本
     */
    private function calculateTextGenerationCost(AiModelConfig $model, string $prompt, array $params): float
    {
        $inputTokens = strlen($prompt) / 4; // 简单估算
        $maxTokens = $params['max_tokens'] ?? 1000;
        $totalTokens = $inputTokens + $maxTokens;

        return round($totalTokens * $model->cost_per_request, 4);
    }
}
