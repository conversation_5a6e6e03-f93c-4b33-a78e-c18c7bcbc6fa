[2025-08-13 16:23:44] [INFO] [req_689c4b90a03b8_1755073424] API请求 {"method":"POST","path":"\/deepseek\/chat\/completions","ip":"127.0.0.1","user_agent":"AI-Tool-API\/1.0"}
[2025-08-13 16:23:44] [ERROR] [req_689c4b90a03b8_1755073424] 异常处理 {"type":"Error","message":"Class \"DeepSeekController\" not found","code":0,"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\aiapi\\index.php","line":154,"trace":"#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\aiapi\\index.php(87): Router->dispatch('POST', 'deepseek\/chat\/c...')\n#1 {main}","platform":"system","timestamp":1755073424,"request_id":"req_689c4b90a0926_1755073424"}
[2025-08-13 16:23:44] [INFO] [req_689c4b90c61e2_1755073424] API请求 {"method":"POST","path":"\/deepseek\/chat\/completions","ip":"127.0.0.1","user_agent":"AI-Tool-API\/1.0"}
[2025-08-13 16:23:44] [ERROR] [req_689c4b90c61e2_1755073424] 异常处理 {"type":"Error","message":"Class \"DeepSeekController\" not found","code":0,"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\aiapi\\index.php","line":154,"trace":"#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\aiapi\\index.php(87): Router->dispatch('POST', 'deepseek\/chat\/c...')\n#1 {main}","platform":"system","timestamp":1755073424,"request_id":"req_689c4b90c67e7_1755073424"}
[2025-08-13 16:27:09] [INFO] [req_689c4c5d9a436_1755073629] API请求 {"method":"POST","path":"\/deepseek\/chat\/completions","ip":"127.0.0.1","user_agent":"AI-Tool-API\/1.0"}
[2025-08-13 16:27:09] [ERROR] [req_689c4c5d9a436_1755073629] 异常处理 {"type":"Error","message":"Class \"DeepSeekController\" not found","code":0,"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\aiapi\\index.php","line":154,"trace":"#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\aiapi\\index.php(87): Router->dispatch('POST', 'deepseek\/chat\/c...')\n#1 {main}","platform":"system","timestamp":1755073629,"request_id":"req_689c4c5d9a8c2_1755073629"}
[2025-08-13 16:48:58] [INFO] [req_689c517a77c0a_1755074938] API请求 {"method":"POST","path":"\/deepseek\/chat\/completions","ip":"127.0.0.1","user_agent":"AI-Tool-API\/1.0"}
[2025-08-13 16:48:58] [ERROR] [req_689c517a77c0a_1755074938] 异常处理 {"type":"Error","message":"Class \"DeepSeekController\" not found","code":0,"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\aiapi\\index.php","line":154,"trace":"#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\aiapi\\index.php(87): Router->dispatch('POST', 'deepseek\/chat\/c...')\n#1 {main}","platform":"system","timestamp":1755074938,"request_id":"req_689c517a780da_1755074938"}
[2025-08-13 17:00:10] [INFO] [req_689c541aea9bc_1755075610] API请求 {"method":"POST","path":"\/deepseek\/chat\/completions","ip":"127.0.0.1","user_agent":"AI-Tool-API\/1.0"}
[2025-08-13 17:00:10] [ERROR] [req_689c541aea9bc_1755075610] 异常处理 {"type":"Error","message":"Class \"DeepSeekController\" not found","code":0,"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\aiapi\\index.php","line":154,"trace":"#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\aiapi\\index.php(87): Router->dispatch('POST', 'deepseek\/chat\/c...')\n#1 {main}","platform":"system","timestamp":1755075610,"request_id":"req_689c541aeb197_1755075610"}
[2025-08-13 17:04:36] [INFO] [req_689c552473681_1755075876] API请求 {"method":"POST","path":"\/deepseek\/chat\/completions","ip":"127.0.0.1","user_agent":"AI-Tool-API\/1.0"}
[2025-08-13 17:04:36] [ERROR] [req_689c552473681_1755075876] 异常处理 {"type":"Error","message":"Class \"DeepSeekController\" not found","code":0,"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\aiapi\\index.php","line":154,"trace":"#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\aiapi\\index.php(87): Router->dispatch('POST', 'deepseek\/chat\/c...')\n#1 {main}","platform":"system","timestamp":1755075876,"request_id":"req_689c552473b4a_1755075876"}
[2025-08-13 17:06:43] [INFO] [req_689c55a344ced_1755076003] API请求 {"method":"POST","path":"\/deepseek\/chat\/completions","ip":"127.0.0.1","user_agent":"AI-Tool-API\/1.0"}
[2025-08-13 17:06:43] [ERROR] [req_689c55a344ced_1755076003] 异常处理 {"type":"Error","message":"Class \"DeepSeekController\" not found","code":0,"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\aiapi\\index.php","line":154,"trace":"#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\aiapi\\index.php(87): Router->dispatch('POST', 'deepseek\/chat\/c...')\n#1 {main}","platform":"system","timestamp":1755076003,"request_id":"req_689c55a3451f2_1755076003"}
[2025-08-13 17:08:25] [INFO] [req_689c5609ed997_1755076105] API请求 {"method":"POST","path":"\/deepseek\/chat\/completions","ip":"127.0.0.1","user_agent":"AI-Tool-API\/1.0"}
[2025-08-13 17:08:25] [ERROR] [req_689c5609ed997_1755076105] 异常处理 {"type":"Error","message":"Class \"DeepSeekController\" not found","code":0,"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\aiapi\\index.php","line":154,"trace":"#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\aiapi\\index.php(87): Router->dispatch('POST', 'deepseek\/chat\/c...')\n#1 {main}","platform":"system","timestamp":1755076105,"request_id":"req_689c5609edda9_1755076105"}
[2025-08-13 17:19:50] [INFO] [req_689c58b662e6a_1755076790] API请求 {"method":"POST","path":"\/deepseek\/chat\/completions","ip":"127.0.0.1","user_agent":"AI-Tool-API\/1.0"}
[2025-08-13 17:19:50] [ERROR] [req_689c58b662e6a_1755076790] 异常处理 {"type":"Error","message":"Class \"DeepSeekController\" not found","code":0,"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\aiapi\\index.php","line":154,"trace":"#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\aiapi\\index.php(87): Router->dispatch('POST', 'deepseek\/chat\/c...')\n#1 {main}","platform":"system","timestamp":1755076790,"request_id":"req_689c58b66362d_1755076790"}
[2025-08-13 17:21:42] [INFO] [req_689c59260e535_1755076902] API请求 {"method":"GET","path":"\/deepseek\/chat\/completions","ip":"127.0.0.1","user_agent":"Augment-VSCode\/1.0"}
[2025-08-13 17:22:02] [INFO] [req_689c593a149ca_1755076922] API请求 {"method":"GET","path":"","ip":"127.0.0.1","user_agent":"Augment-VSCode\/1.0"}
[2025-08-13 17:22:18] [INFO] [req_689c594ae93da_1755076938] API请求 {"method":"GET","path":"\/status","ip":"127.0.0.1","user_agent":"Augment-VSCode\/1.0"}
[2025-08-13 17:33:08] [INFO] [req_689c5bd4ef63f_1755077588] API请求 {"method":"GET","path":"\/deepseek\/chat\/completions","ip":"127.0.0.1","user_agent":"Augment-VSCode\/1.0"}
[2025-08-13 17:35:13] [INFO] [req_689c5c51416a9_1755077713] API请求 {"method":"GET","path":"\/deepseek\/chat\/completions","ip":"::1","user_agent":"Augment-VSCode\/1.0"}
[2025-08-13 17:35:40] [INFO] [req_689c5c6cc6e80_1755077740] API请求 {"method":"GET","path":"\/deepseek\/chat\/completions","ip":"::1","user_agent":"Augment-VSCode\/1.0"}
[2025-08-13 17:37:18] [INFO] [req_689c5cce8fd49_1755077838] API请求 {"method":"POST","path":"\/deepseek\/chat\/completions","ip":"::1","user_agent":"Mozilla\/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell\/5.1.26100.1591"}
[2025-08-13 17:37:18] [ERROR] [req_689c5cce8fd49_1755077838] 异常处理 {"type":"Error","message":"Call to undefined method Logger::logError()","code":0,"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\aiapi\\controllers\\DeepSeekController.php","line":74,"trace":"#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\aiapi\\index.php(169): DeepSeekController->chatCompletions(Array)\n#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\aiapi\\index.php(87): Router->dispatch('POST', 'deepseek\/chat\/c...')\n#2 {main}","platform":"system","timestamp":1755077838,"request_id":"req_689c5cce91033_1755077838"}
[2025-08-13 17:37:47] [INFO] [req_689c5cebc1229_1755077867] API请求 {"method":"GET","path":"\/deepseek\/models","ip":"::1","user_agent":"Augment-VSCode\/1.0"}
[2025-08-13 17:37:47] [INFO] [req_689c5cebc1229_1755077867] API性能监控 {"platform":"deepseek","endpoint":"deepseek\/models","method":"GET","duration":0.8420944213867188,"memory_used":0,"timestamp":1755077867,"date":"2025-08-13","hour":"17"}
[2025-08-13 17:38:18] [INFO] [req_689c5d0a29745_1755077898] API请求 {"method":"POST","path":"\/deepseek\/chat\/completions","ip":"::1","user_agent":"unknown"}
[2025-08-13 17:38:18] [INFO] [req_689c5d0a29745_1755077898] API请求 {"platform":"deepseek","endpoint":"chat\/completions","method":"POST","duration":"0.69ms","params_count":2,"response_size":989,"params":{"model":"deepseek-chat","messages":[{"role":"user","content":"请写一首关于夏天的诗"}]},"response":{"id":"chatcmpl-689c5d0a2a27c","object":"chat.completion","created":1755077898,"model":"deepseek-chat","choices":[{"index":0,"message":{"role":"assistant","content":"感谢您的提问。作为DeepSeek AI助手，我很乐意为您提供帮助。基于您的输入，我理解您希望获得相关的信息和建议。\n\n这个问题涉及多个方面的考虑。我建议从以下几个维度来分析：技术可行性、资源配置、时间安排、风险评估等。通过综合分析，我们可以找到最优的解决方案。"},"finish_reason":"stop"}],"usage":{"prompt_tokens":10,"completion_tokens":107,"total_tokens":117}}}
[2025-08-13 17:38:18] [INFO] [req_689c5d0a29745_1755077898] API性能监控 {"platform":"deepseek","endpoint":"deepseek\/chat\/completions","method":"POST","duration":2.1500587463378906,"memory_used":0,"timestamp":1755077898,"date":"2025-08-13","hour":"17"}
[2025-08-13 17:40:10] [INFO] [req_689c5d7ac2b0d_1755078010] API请求 {"method":"GET","path":"\/deepseek\/models","ip":"127.0.0.1","user_agent":"Augment-VSCode\/1.0"}
[2025-08-13 17:40:10] [INFO] [req_689c5d7ac2b0d_1755078010] API性能监控 {"platform":"deepseek","endpoint":"deepseek\/models","method":"GET","duration":1.3759136199951172,"memory_used":0,"timestamp":1755078010,"date":"2025-08-13","hour":"17"}
[2025-08-13 17:43:40] [INFO] [req_689c5e4c4430d_1755078220] API请求 {"method":"GET","path":"\/deepseek\/models","ip":"127.0.0.1","user_agent":"Augment-VSCode\/1.0"}
[2025-08-13 17:43:40] [INFO] [req_689c5e4c4430d_1755078220] API性能监控 {"platform":"deepseek","endpoint":"deepseek\/models","method":"GET","duration":2.3009777069091797,"memory_used":0,"timestamp":1755078220,"date":"2025-08-13","hour":"17"}
[2025-08-13 17:52:42] [INFO] [req_689c606aaea8f_1755078762] API请求 {"method":"GET","path":"\/deepseek\/models","ip":"127.0.0.1","user_agent":"Augment-VSCode\/1.0"}
[2025-08-13 17:52:42] [INFO] [req_689c606aaea8f_1755078762] DeepSeek控制器初始化 {"platform":"deepseek","config_loaded":true}
[2025-08-13 17:52:42] [INFO] [req_689c606aaea8f_1755078762] API性能监控 {"platform":"deepseek","endpoint":"deepseek\/models","method":"GET","duration":3.612995147705078,"memory_used":0,"timestamp":1755078762,"date":"2025-08-13","hour":"17"}
[2025-08-13 17:54:09] [INFO] [req_689c60c18ffa9_1755078849] API请求 {"method":"GET","path":"\/deepseek\/chat\/completions","ip":"127.0.0.1","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36"}
[2025-08-13 17:54:13] [INFO] [req_689c60c50dcb6_1755078853] API请求 {"method":"POST","path":"\/deepseek\/chat\/completions","ip":"127.0.0.1","user_agent":"TestScript\/1.0"}
[2025-08-13 17:54:13] [INFO] [req_689c60c50dcb6_1755078853] DeepSeek控制器初始化 {"platform":"deepseek","config_loaded":true}
[2025-08-13 17:54:13] [INFO] [req_689c60c50dcb6_1755078853] API请求 {"platform":"deepseek","endpoint":"chat\/completions","method":"POST","duration":"0.93ms","params_count":2,"response_size":989,"params":{"model":"deepseek-chat","messages":[{"role":"user","content":"请写一首关于夏天的诗"}]},"response":{"id":"chatcmpl-689c60c50eb52","object":"chat.completion","created":1755078853,"model":"deepseek-chat","choices":[{"index":0,"message":{"role":"assistant","content":"感谢您的提问。作为DeepSeek AI助手，我很乐意为您提供帮助。基于您的输入，我理解您希望获得相关的信息和建议。\n\n这个问题涉及多个方面的考虑。我建议从以下几个维度来分析：技术可行性、资源配置、时间安排、风险评估等。通过综合分析，我们可以找到最优的解决方案。"},"finish_reason":"stop"}],"usage":{"prompt_tokens":10,"completion_tokens":107,"total_tokens":117}}}
[2025-08-13 17:54:13] [INFO] [req_689c60c50dcb6_1755078853] API性能监控 {"platform":"deepseek","endpoint":"deepseek\/chat\/completions","method":"POST","duration":3.490924835205078,"memory_used":0,"timestamp":1755078853,"date":"2025-08-13","hour":"17"}
[2025-08-13 17:55:52] [INFO] [req_689c61284f0c2_1755078952] API请求 {"method":"GET","path":"\/deepseek\/models","ip":"127.0.0.1","user_agent":"Augment-VSCode\/1.0"}
[2025-08-13 17:55:52] [INFO] [req_689c61284f0c2_1755078952] DeepSeek控制器初始化 {"platform":"deepseek","config_loaded":true}
[2025-08-13 17:55:52] [INFO] [req_689c61284f0c2_1755078952] API性能监控 {"platform":"deepseek","endpoint":"deepseek\/models","method":"GET","duration":1.6469955444335938,"memory_used":0,"timestamp":1755078952,"date":"2025-08-13","hour":"17"}
[2025-08-13 17:56:06] [INFO] [req_689c613684c2c_1755078966] API请求 {"method":"POST","path":"\/deepseek\/chat\/completions","ip":"127.0.0.1","user_agent":"TestScript\/1.0"}
[2025-08-13 17:56:06] [INFO] [req_689c613684c2c_1755078966] DeepSeek控制器初始化 {"platform":"deepseek","config_loaded":true}
[2025-08-13 17:56:06] [INFO] [req_689c613684c2c_1755078966] API请求 {"platform":"deepseek","endpoint":"chat\/completions","method":"POST","duration":"0.92ms","params_count":2,"response_size":929,"params":{"model":"deepseek-chat","messages":[{"role":"user","content":"请写一首关于夏天的诗"}]},"response":{"id":"chatcmpl-689c613685a7d","object":"chat.completion","created":1755078966,"model":"deepseek-chat","choices":[{"index":0,"message":{"role":"assistant","content":"根据您的描述，我认为这个问题可以从多个角度来考虑。让我为您提供一些思路和建议。\n\n这个问题涉及多个方面的考虑。我建议从以下几个维度来分析：技术可行性、资源配置、时间安排、风险评估等。通过综合分析，我们可以找到最优的解决方案。"},"finish_reason":"stop"}],"usage":{"prompt_tokens":10,"completion_tokens":99,"total_tokens":109}}}
[2025-08-13 17:56:06] [INFO] [req_689c613684c2c_1755078966] API性能监控 {"platform":"deepseek","endpoint":"deepseek\/chat\/completions","method":"POST","duration":3.269195556640625,"memory_used":0,"timestamp":1755078966,"date":"2025-08-13","hour":"17"}
[2025-08-13 21:44:11] [INFO] [req_689c96abf0223_1755092651] API请求 {"method":"POST","path":"\/deepseek\/chat\/completions","ip":"127.0.0.1","user_agent":"AI-Tool-API\/1.0"}
[2025-08-13 21:44:11] [INFO] [req_689c96abf0223_1755092651] DeepSeek控制器初始化 {"platform":"deepseek","config_loaded":true}
[2025-08-13 21:44:11] [INFO] [req_689c96abf0223_1755092651] API请求 {"platform":"deepseek","endpoint":"chat\/completions","method":"POST","duration":"0.86ms","params_count":5,"response_size":970,"params":{"model":"deepseek-chat","messages":[{"role":"user","content":""}],"max_tokens":"1000","temperature":"0.7","top_p":0.9},"response":{"id":"chatcmpl-689c96abf1089","object":"chat.completion","created":1755092651,"model":"deepseek-chat","choices":[{"index":0,"message":{"role":"assistant","content":"感谢您的提问。作为DeepSeek AI助手，我很乐意为您提供帮助。基于您的输入，我理解您希望获得相关的信息和建议。\n\n首先，我们需要明确目标和需求。然后制定合理的计划，分步骤实施。在执行过程中要注意细节，及时调整策略。最后进行总结和反思，为下次提供经验。"},"finish_reason":"stop"}],"usage":{"prompt_tokens":0,"completion_tokens":104,"total_tokens":104}}}
[2025-08-13 21:44:11] [INFO] [req_689c96abf0223_1755092651] API性能监控 {"platform":"deepseek","endpoint":"deepseek\/chat\/completions","method":"POST","duration":3.2689571380615234,"memory_used":0,"timestamp":1755092651,"date":"2025-08-13","hour":"21"}
